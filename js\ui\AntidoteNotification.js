// ===== ANTIDOTE ACQUISITION NOTIFICATION =====
// Displays an overlay when an antidote is obtained

class AntidoteNotification {
    constructor() {
        this.modal = null;
        this.isShowing = false;
        this.imageLoaded = false;
        this.init();
    }
    
    init() {
        // Get the modal element
        this.modal = document.getElementById('antidoteModal');
        if (!this.modal) {
            console.error('Antidote modal not found in DOM');
            return;
        }
        
        // Set up click handler to dismiss the notification
        this.modal.addEventListener('click', () => {
            this.dismissNotification();
        });
        
        // Preload the antidote image
        this.preloadImage();
        
        console.log('Antidote notification system initialized');
    }
    
    // Preload the antidote image
    async preloadImage() {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => {
                this.imageLoaded = true;
                console.log('Antidote image preloaded successfully');
                resolve();
            };
            img.onerror = () => {
                console.error('Failed to preload antidote image');
                reject();
            };
            img.src = 'assets/images/antidote.png';
        });
    }
    
    // Show the antidote notification
    async showNotification() {
        if (!this.modal || this.isShowing) return;
        
        console.log('Showing antidote notification');
        
        // Ensure the image is loaded before showing
        if (!this.imageLoaded) {
            await this.preloadImage();
        }
        
        // Show the modal
        this.modal.classList.add('active');
        this.isShowing = true;
        
        // Add a slight delay for better visual effect
        setTimeout(() => {
            if (this.modal) {
                this.modal.style.opacity = '1';
            }
        }, 50);
        
        // Auto-dismiss after 5 seconds if user doesn't click
        setTimeout(() => {
            if (this.isShowing) {
                this.dismissNotification();
            }
        }, 5000);
    }
    
    // Dismiss the notification
    dismissNotification() {
        if (!this.modal || !this.isShowing) return;
        
        console.log('Dismissing antidote notification');
        
        // Fade out effect
        this.modal.style.opacity = '0';
        
        // Hide the modal after fade out
        setTimeout(() => {
            if (this.modal) {
                this.modal.classList.remove('active');
                this.isShowing = false;
            }
        }, 300);
    }
    
    // Check if notification is currently showing
    isNotificationShowing() {
        return this.isShowing;
    }

    // Debug method to test the notification
    testNotification() {
        console.log('Testing antidote notification...');
        this.showNotification();
    }
}

// Create global instance
const antidoteNotification = new AntidoteNotification();

// Make available globally for browser
if (typeof window !== 'undefined') {
    window.antidoteNotification = antidoteNotification;

    // Add global test function for debugging
    window.testAntidoteOverlay = function() {
        console.log('Testing antidote overlay...');
        if (window.antidoteNotification) {
            window.antidoteNotification.testNotification();
        } else {
            console.error('Antidote notification system not available');
        }
    };

    // Add test function to simulate purchasing an antidote
    window.testAntidotePurchase = function() {
        console.log('Testing antidote purchase...');
        if (window.gameState) {
            window.gameState.addItem('antidote', 1);
        } else {
            console.error('Game state not available');
        }
    };
}
