// ===== DUNGEON GENERATION SYSTEM =====
// Procedural dungeon generation with rooms, corridors, and features

class Dungeon {
    constructor() {
        this.size = CONSTANTS.DUNGEON_SIZE;
    }
    
    // Generate all floors at once
    generateAllFloors() {
        const floors = [];
        for (let floor = 1; floor <= CONSTANTS.MAX_FLOORS; floor++) {
            floors.push(this.generateFloor(floor));
        }
        return floors;
    }
    
    // Generate a single floor
    generateFloor(floorNumber) {
        const floor = this.initializeFloor();
        
        // Generate the dungeon layout
        this.generateRoomsAndCorridors(floor, floorNumber);
        
        // Add special features
        if (floorNumber < CONSTANTS.MAX_FLOORS) {
            this.addStairs(floor);
        }
        
        this.addHealingPoints(floor);
        this.addEnemies(floor, floorNumber);
        
        // Add trap pits (after stairs, healing points, and enemies)
        this.addTrapPits(floor, floorNumber);
        
        // Add final boss on the last floor
        if (floorNumber === CONSTANTS.MAX_FLOORS) {
            this.addFinalBoss(floor);
        }
        
        return floor;
    }
    
    // Initialize empty floor with walls
    initializeFloor() {
        const floor = [];
        for (let y = 0; y < this.size; y++) {
            floor[y] = [];
            for (let x = 0; x < this.size; x++) {
                floor[y][x] = {
                    type: 'wall',
                    visited: false,
                    enemy: false,
                    enemyRespawned: false,
                    trapPit: false
                };
            }
        }
        return floor;
    }
    
    // Generate rooms and connecting corridors
    generateRoomsAndCorridors(floor, floorNumber) {
        const rooms = [];
        const numRooms = 6 + Math.floor(floorNumber / 2);
        
        // Generate rooms
        for (let i = 0; i < numRooms; i++) {
            const room = this.generateRoom();
            if (!this.checkRoomOverlap(room, rooms)) {
                rooms.push(room);
                this.carveRoom(floor, room);
                this.addDoorsToRoom(floor, room);
            }
        }
        
        // Connect rooms with corridors
        for (let i = 0; i < rooms.length - 1; i++) {
            this.createCorridor(floor, rooms[i], rooms[i + 1]);
        }
        
        // Create entrance area
        this.createEntranceArea(floor, rooms);
        
        // Add shops to doors
        this.addShopsToFloor(floor, floorNumber);
    }
    
    // Generate a random room
    generateRoom() {
        const width = 3 + Math.floor(Math.random() * 4);
        const height = 3 + Math.floor(Math.random() * 4);
        const x = 1 + Math.floor(Math.random() * (this.size - width - 2));
        const y = 1 + Math.floor(Math.random() * (this.size - height - 2));
        
        return { x, y, width, height };
    }
    
    // Check if room overlaps with existing rooms
    checkRoomOverlap(room, existingRooms) {
        for (const existingRoom of existingRooms) {
            if (room.x < existingRoom.x + existingRoom.width + 1 && 
                room.x + room.width + 1 > existingRoom.x &&
                room.y < existingRoom.y + existingRoom.height + 1 && 
                room.y + room.height + 1 > existingRoom.y) {
                return true;
            }
        }
        return false;
    }
    
    // Carve out room floor tiles
    carveRoom(floor, room) {
        for (let y = room.y; y < room.y + room.height; y++) {
            for (let x = room.x; x < room.x + room.width; x++) {
                floor[y][x].type = 'floor';
            }
        }
    }
    
    // Add doors to room walls
    addDoorsToRoom(floor, room) {
        const doors = [];
        
        // Potential door positions
        if (room.y > 1) {
            doors.push({ x: room.x + Math.floor(room.width / 2), y: room.y - 1 });
        }
        if (room.y + room.height < this.size - 1) {
            doors.push({ x: room.x + Math.floor(room.width / 2), y: room.y + room.height });
        }
        if (room.x > 1) {
            doors.push({ x: room.x - 1, y: room.y + Math.floor(room.height / 2) });
        }
        if (room.x + room.width < this.size - 1) {
            doors.push({ x: room.x + room.width, y: room.y + Math.floor(room.height / 2) });
        }
        
        // Place 1-2 doors randomly
        const numDoors = 1 + Math.floor(Math.random() * 2);
        for (let i = 0; i < Math.min(numDoors, doors.length); i++) {
            const door = doors[Math.floor(Math.random() * doors.length)];
            floor[door.y][door.x].type = 'door';
            floor[door.y][door.x].opened = false; // Doors start closed
        }
    }
    
    // Create corridor between two rooms
    createCorridor(floor, room1, room2) {
        const startX = room1.x + Math.floor(room1.width / 2);
        const startY = room1.y + Math.floor(room1.height / 2);
        const endX = room2.x + Math.floor(room2.width / 2);
        const endY = room2.y + Math.floor(room2.height / 2);
        
        // Horizontal corridor
        const minX = Math.min(startX, endX);
        const maxX = Math.max(startX, endX);
        for (let x = minX; x <= maxX; x++) {
            floor[startY][x].type = 'floor';
        }
        
        // Vertical corridor
        const minY = Math.min(startY, endY);
        const maxY = Math.max(startY, endY);
        for (let y = minY; y <= maxY; y++) {
            floor[y][endX].type = 'floor';
        }
    }
    
    // Create entrance area for player starting position
    createEntranceArea(floor, rooms) {
        // Player starting position
        floor[CONSTANTS.PLAYER_START_Y][CONSTANTS.PLAYER_START_X].type = 'floor';
        floor[CONSTANTS.PLAYER_START_Y - 1][CONSTANTS.PLAYER_START_X].type = 'floor';
        
        // Connect to nearest room
        if (rooms.length > 0) {
            const nearestRoom = rooms.reduce((closest, room) => {
                const dist = Math.abs(room.x + room.width/2 - CONSTANTS.PLAYER_START_X) + 
                           Math.abs(room.y + room.height/2 - CONSTANTS.PLAYER_START_Y);
                const closestDist = Math.abs(closest.x + closest.width/2 - CONSTANTS.PLAYER_START_X) + 
                                  Math.abs(closest.y + closest.height/2 - CONSTANTS.PLAYER_START_Y);
                return dist < closestDist ? room : closest;
            });
            
            this.createCorridor(floor, 
                { x: CONSTANTS.PLAYER_START_X, y: CONSTANTS.PLAYER_START_Y, width: 1, height: 1 }, 
                nearestRoom
            );
        }
    }
    
    // Add stairs to next floor
    addStairs(floor) {
        let stairsPlaced = false;
        let attempts = 0;
        
        while (!stairsPlaced && attempts < 100) {
            const x = Math.floor(Math.random() * this.size);
            const y = Math.floor(Math.random() * this.size);
            
            if (floor[y][x].type === 'floor' && 
                !(x === CONSTANTS.PLAYER_START_X && y === CONSTANTS.PLAYER_START_Y)) {
                floor[y][x].type = 'stairs';
                stairsPlaced = true;
            }
            attempts++;
        }
    }
    
    // Add healing points
    addHealingPoints(floor) {
        const numHealPoints = 2 + Math.floor(Math.random() * 2);
        let placed = 0;
        let attempts = 0;
        
        while (placed < numHealPoints && attempts < 100) {
            const x = Math.floor(Math.random() * this.size);
            const y = Math.floor(Math.random() * this.size);
            
            if (floor[y][x].type === 'floor' && 
                !(x === CONSTANTS.PLAYER_START_X && y === CONSTANTS.PLAYER_START_Y)) {
                floor[y][x].type = 'heal';
                placed++;
            }
            attempts++;
        }
    }
    
    // Add enemies to the floor
    addEnemies(floor, floorNumber) {
        const baseEnemyCount = CONSTANTS.ENEMY.BASE_COUNT + floorNumber * CONSTANTS.ENEMY.COUNT_PER_FLOOR;
        let placed = 0;
        let attempts = 0;
        
        while (placed < baseEnemyCount && attempts < 200) {
            const x = Math.floor(Math.random() * this.size);
            const y = Math.floor(Math.random() * this.size);
            
            if (floor[y][x].type === 'floor' && 
                !(x === CONSTANTS.PLAYER_START_X && y === CONSTANTS.PLAYER_START_Y)) {
                floor[y][x].enemy = true;
                placed++;
            }
            attempts++;
        }
    }
    
    // Add trap pits to floor cells with no surrounding walls
    addTrapPits(floor, floorNumber) {
        // Calculate spawn chance based on floor: base rate + (floor - 1) * increase rate
        const spawnChance = CONSTANTS.TRAP_PIT.BASE_SPAWN_CHANCE + 
                           (floorNumber - 1) * CONSTANTS.TRAP_PIT.FLOOR_INCREASE_RATE;
        
        for (let y = 0; y < this.size; y++) {
            for (let x = 0; x < this.size; x++) {
                // Check if this cell is eligible for a trap pit
                if (this.canPlaceTrapPit(floor, x, y)) {
                    // Apply dynamic spawn chance based on floor number
                    if (Math.random() < spawnChance) {
                        floor[y][x].trapPit = true;
                    }
                }
            }
        }
    }
    
    // Check if a trap pit can be placed at the given coordinates
    canPlaceTrapPit(floor, x, y) {
        const cell = floor[y][x];
        
        // Must be a floor cell
        if (cell.type !== 'floor') {
            return false;
        }
        
        // Cannot be player starting position
        if (x === CONSTANTS.PLAYER_START_X && y === CONSTANTS.PLAYER_START_Y) {
            return false;
        }
        
        // Cannot have an enemy
        if (cell.enemy) {
            return false;
        }
        
        // Check if there are no surrounding walls
        return this.hasNoSurroundingWalls(floor, x, y);
    }
    
    // Check if a cell has no walls in adjacent cells (8-directional check)
    hasNoSurroundingWalls(floor, x, y) {
        for (let dy = -1; dy <= 1; dy++) {
            for (let dx = -1; dx <= 1; dx++) {
                // Skip the center cell
                if (dx === 0 && dy === 0) continue;
                
                const checkX = x + dx;
                const checkY = y + dy;
                
                // Check bounds
                if (checkX < 0 || checkX >= this.size || checkY < 0 || checkY >= this.size) {
                    return false; // Out of bounds is considered a wall
                }
                
                // If any surrounding cell is a wall, this position is not eligible
                if (floor[checkY][checkX].type === 'wall') {
                    return false;
                }
            }
        }
        
        return true; // No walls found in surrounding cells
    }
    
    // Add shops to doors on the floor
    addShopsToFloor(floor, floorNumber = 1) {
        // Find all doors on the floor
        const doors = [];
        for (let y = 0; y < this.size; y++) {
            for (let x = 0; x < this.size; x++) {
                if (floor[y][x].type === 'door') {
                    doors.push({ x, y });
                }
            }
        }

        if (doors.length === 0) return;

        // Determine the number of shops based on floor and available doors
        let numShops = Math.min(doors.length, 2 + Math.floor(Math.random() * 3));

        // On floors 3+, ensure we have room for ALCHEMIST shop
        if (floorNumber >= 3) {
            numShops = Math.min(doors.length, Math.max(numShops, 3)); // At least 3 shops to include ALCHEMIST
        }

        // Shuffle doors array to randomize selection
        for (let i = doors.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [doors[i], doors[j]] = [doors[j], doors[i]];
        }

        // Select doors for shops
        const selectedDoors = doors.slice(0, numShops);

        // Shop types: Chapel (priest) is mandatory, ALCHEMIST is mandatory on floors 3+
        const shopTypes = ['priest']; // Chapel is always included

        // Add ALCHEMIST shop on floors 3 and above
        if (floorNumber >= 3) {
            shopTypes.push('alchemist');
        }

        const otherShops = ['merchant', 'fighter', 'witch'];

        // Add random shops to fill remaining slots
        for (let i = shopTypes.length; i < numShops; i++) {
            const randomShop = otherShops[Math.floor(Math.random() * otherShops.length)];
            shopTypes.push(randomShop);
        }

        // Assign shop types to selected doors
        for (let i = 0; i < selectedDoors.length; i++) {
            const door = selectedDoors[i];
            const shopType = shopTypes[i];

            // Assign shop type to door
            floor[door.y][door.x].shopType = shopType;

            // Calculate shop position (opposite side of door from room)
            const shopPosition = this.calculateShopPosition(floor, door.x, door.y);
            if (shopPosition) {
                floor[door.y][door.x].shopPosition = shopPosition;
            }
        }
    }
    
    // Calculate the shop position on the opposite side of the door from the room
    calculateShopPosition(floor, doorX, doorY) {
        // Check all 4 directions to find which side has a room (floor)
        const directions = [
            { dx: 0, dy: -1 }, // North
            { dx: 1, dy: 0 },  // East
            { dx: 0, dy: 1 },  // South
            { dx: -1, dy: 0 }  // West
        ];
        
        for (const dir of directions) {
            const roomX = doorX + dir.dx;
            const roomY = doorY + dir.dy;
            
            // Check if this direction leads to a room (floor)
            if (roomX >= 0 && roomX < this.size && roomY >= 0 && roomY < this.size) {
                if (floor[roomY][roomX].type === 'floor') {
                    // This side has the room, so shop is on the opposite side
                    const shopX = doorX - dir.dx;
                    const shopY = doorY - dir.dy;
                    
                    // Ensure shop position is within bounds
                    if (shopX >= 0 && shopX < this.size && shopY >= 0 && shopY < this.size) {
                        return { x: shopX, y: shopY };
                    }
                }
            }
        }
        
        return null; // Could not determine shop position
    }
    
    // Add final boss
    addFinalBoss(floor) {
        // Find center of largest room for boss placement
        for (let y = 1; y < this.size - 1; y++) {
            for (let x = 1; x < this.size - 1; x++) {
                if (floor[y][x].type === 'floor' && 
                    !(x === CONSTANTS.PLAYER_START_X && y === CONSTANTS.PLAYER_START_Y)) {
                    floor[y][x].type = 'boss';
                    floor[y][x].enemy = true;
                    return;
                }
            }
        }
    }
}

// Create global instance
const dungeonGenerator = new Dungeon();

// Make available globally for browser
if (typeof window !== 'undefined') {
    window.dungeonGenerator = dungeonGenerator;
}
