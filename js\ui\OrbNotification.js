// ===== ORB ACQUISITION NOTIFICATION =====
// Displays an overlay when an orb is obtained

class OrbNotification {
    constructor() {
        this.modal = null;
        this.isShowing = false;
        this.imageLoaded = false;
        this.init();
    }
    
    init() {
        // Get the modal element
        this.modal = document.getElementById('orbModal');
        if (!this.modal) {
            console.error('Orb modal not found in DOM');
            return;
        }
        
        // Set up click handler to dismiss the notification
        this.modal.addEventListener('click', () => {
            this.dismissNotification();
        });
        
        // Preload the orb image
        this.preloadImage();
        
        console.log('Orb notification system initialized');
    }
    
    // Preload the orb image for smooth display
    preloadImage() {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => {
                this.imageLoaded = true;
                console.log('Orb image preloaded successfully');
                resolve();
            };
            img.onerror = () => {
                console.error('Failed to preload orb image');
                reject();
            };
            img.src = 'assets/images/orb.png';
        });
    }
    
    // Show the orb notification
    async showNotification() {
        if (!this.modal || this.isShowing) return;
        
        console.log('Showing orb notification');
        
        // Ensure the image is loaded before showing
        if (!this.imageLoaded) {
            await this.preloadImage();
        }
        
        // Show the modal
        this.modal.classList.add('active');
        this.isShowing = true;
        
        // Add a slight delay for better visual effect
        setTimeout(() => {
            if (this.modal) {
                this.modal.style.opacity = '1';
            }
        }, 50);
        
        // Auto-dismiss after 5 seconds if user doesn't click
        setTimeout(() => {
            if (this.isShowing) {
                this.dismissNotification();
            }
        }, 5000);
    }
    
    // Dismiss the orb notification
    dismissNotification() {
        if (!this.modal || !this.isShowing) return;
        
        console.log('Dismissing orb notification');
        
        // Fade out effect
        this.modal.style.opacity = '0';
        
        // Hide the modal after fade out
        setTimeout(() => {
            if (this.modal) {
                this.modal.classList.remove('active');
                this.isShowing = false;
            }
        }, 300);
    }
}

// Initialize the orb notification system when the page loads
document.addEventListener('DOMContentLoaded', () => {
    window.orbNotification = new OrbNotification();
});
