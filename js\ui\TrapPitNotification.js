// ===== TRAP PIT DAMAGE NOTIFICATION =====
// Displays an overlay when players take damage from trap pits

class TrapPitNotification {
    constructor() {
        this.modal = null;
        this.isShowing = false;
        this.imageLoaded = false;
        this.init();
    }
    
    init() {
        // Get the modal element
        this.modal = document.getElementById('trapPitModal');
        if (!this.modal) {
            console.error('Trap pit modal not found in DOM');
            return;
        }
        
        // Set up click handler to dismiss the notification
        this.modal.addEventListener('click', () => {
            this.dismissNotification();
        });
        
        // Preload the trap pit image
        this.preloadImage();
        
        console.log('Trap pit notification system initialized');
    }
    
    // Preload the trap pit image
    async preloadImage() {
        try {
            const imagePath = 'assets/images/pitfall.png';
            
            if (window.imageManager) {
                const result = await imageManager.preloadImage(imagePath);
                this.imageLoaded = result.success;
                if (!result.success) {
                    console.warn('Failed to preload trap pit image:', imagePath);
                }
            } else {
                // Fallback if ImageManager is not available
                const img = new Image();
                img.onload = () => { this.imageLoaded = true; };
                img.onerror = () => { console.warn('Failed to preload trap pit image:', imagePath); };
                img.src = imagePath;
            }
        } catch (error) {
            console.error('Error preloading trap pit image:', error);
        }
    }
    
    // Show the trap pit notification with damage messages
    async showNotification(damageMessages = []) {
        if (!this.modal || this.isShowing) return;
        
        console.log('Showing trap pit notification');
        
        // Ensure the image is loaded before showing
        if (!this.imageLoaded) {
            await this.preloadImage();
        }
        
        // Update the message content if provided
        if (damageMessages.length > 0) {
            const messageContainer = this.modal.querySelector('.trap-pit-messages');
            if (messageContainer) {
                messageContainer.innerHTML = damageMessages.map(msg => `<div class="trap-pit-message-line">${msg}</div>`).join('');
            }
        }
        
        // Show the modal
        this.modal.classList.add('active');
        this.isShowing = true;
        
        // Add a slight delay for better visual effect
        setTimeout(() => {
            if (this.modal) {
                this.modal.style.opacity = '1';
            }
        }, 50);
        
        // Auto-dismiss after 3 seconds (as specified in requirements)
        setTimeout(() => {
            if (this.isShowing) {
                this.dismissNotification();
            }
        }, 3000);
    }
    
    // Dismiss the notification
    dismissNotification() {
        if (!this.modal || !this.isShowing) return;
        
        console.log('Dismissing trap pit notification');
        
        // Fade out
        this.modal.style.opacity = '0';
        
        // Remove after transition
        setTimeout(() => {
            if (this.modal) {
                this.modal.classList.remove('active');
                this.isShowing = false;
            }
        }, 300);
    }
}

// Create global instance
const trapPitNotification = new TrapPitNotification();

// Make available globally for browser
if (typeof window !== 'undefined') {
    window.trapPitNotification = trapPitNotification;
}
