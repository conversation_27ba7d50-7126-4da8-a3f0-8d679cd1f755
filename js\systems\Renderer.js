// ===== RENDERING SYSTEM =====
// 3D perspective rendering, minimap, and visual effects

class Renderer {
    constructor() {
        this.canvas = null;
        this.ctx = null;
    }
    
    // Initialize renderer with canvas
    initialize(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        gameState.setCanvas(canvas);
    }
    
    // Main render method
    render() {
        if (!this.ctx) return;

        // Clear canvas
        this.ctx.fillStyle = '#000011';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // Render 3D perspective view
        this.render3D();

        // Add scan line effect
        this.addScanLines();

        // Update minimap to ensure it stays current
        this.updateMinimap();
    }
    
    // Render 3D perspective view
    render3D() {
        const ctx = this.ctx;
        const canvas = this.canvas;

        // Get direction vectors
        const dir = gameState.getCurrentDirectionVector();
        const leftDir = gameState.getLeftDirectionVector();
        const rightDir = gameState.getRightDirectionVector();

        // Create background gradient
        const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
        gradient.addColorStop(0, '#001122');
        gradient.addColorStop(1, '#000000');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // Draw side walls
        this.drawSideWalls(leftDir, rightDir);

        // Draw perspective view forward
        this.drawForwardView(dir);

        // Add atmospheric effects (floating particles, etc.)
        this.drawAtmosphericEffects();
    }
    
    // Draw left and right side walls as trapezoidal blocks with proper perspective
    drawSideWalls(leftDir, rightDir) {
        const forwardDir = gameState.getCurrentDirectionVector();

        // Collect side wall segments for both left and right sides
        const leftWallSegments = this.collectTrapezoidalSideWalls(leftDir, forwardDir, 'left');
        const rightWallSegments = this.collectTrapezoidalSideWalls(rightDir, forwardDir, 'right');

        // Render side walls from farthest to nearest for proper depth ordering
        this.renderTrapezoidalSideWalls(leftWallSegments, 'left');
        this.renderTrapezoidalSideWalls(rightWallSegments, 'right');
    }

    // Collect trapezoidal side wall segments that only appear when walls are adjacent to corridor
    // Implements proper occlusion - stops collecting segments beyond walls in the forward direction
    collectTrapezoidalSideWalls(sideDir, forwardDir, side) {
        const segments = [];
        const corridorWidth = this.canvas.width * 0.7; // Same as drawForwardView

        // Check each distance from 0 to 5 grids for wall presence
        // Include distance 0 to render side walls at player's immediate position
        for (let distance = 0; distance <= 5; distance++) {
            // Calculate the forward position at this distance
            const forwardX = gameState.player.x + (forwardDir.x * distance);
            const forwardY = gameState.player.y + (forwardDir.y * distance);

            // Check if there's a wall in the forward direction at this distance
            // If so, we need to stop collecting segments beyond this point for proper occlusion
            const forwardCellInfo = this.getCellInfo(forwardX, forwardY);

            // Calculate the side wall position at this distance
            const sideX = forwardX + sideDir.x;
            const sideY = forwardY + sideDir.y;

            // Only render side wall if there's actually a wall adjacent to the corridor
            if (this.isWallAt(sideX, sideY)) {
                // Special handling for distance 0 to ensure proper perspective calculation
                // Use a minimum distance to avoid excessive perspective values
                const effectiveDistance = Math.max(distance, 0.1);
                const perspective = 1 / (effectiveDistance + 0.5);

                // Calculate wall dimensions using same perspective system as forward view
                const wallHeight = this.canvas.height * perspective * 0.8;
                const wallY = (this.canvas.height - wallHeight) / 2;

                // Calculate corridor boundaries at this distance for proper alignment
                const corridorWidthAtDistance = corridorWidth * perspective;
                const centerX = this.canvas.width / 2;
                const leftCorridorBoundary = centerX - corridorWidthAtDistance / 2;
                const rightCorridorBoundary = centerX + corridorWidthAtDistance / 2;
                segments.push({
                    distance: distance,
                    perspective: perspective,
                    wallHeight: wallHeight,
                    wallY: wallY,
                    leftCorridorBoundary: leftCorridorBoundary,
                    rightCorridorBoundary: rightCorridorBoundary,
                    corridorWidthAtDistance: corridorWidthAtDistance
                });
            }

            // Stop collecting segments if we hit a wall in the forward direction
            // This implements proper occlusion - walls block the view of anything behind them
            // IMPORTANT: Only apply occlusion for distances > 0 to allow side walls at player position
            if (forwardCellInfo.isWall && distance > 0) {
                break;
            }
        }

        return segments;
    }

    // Render trapezoidal side walls with proper depth ordering and perspective
    renderTrapezoidalSideWalls(segments, side) {
        if (segments.length === 0) return;

        const ctx = this.ctx;

        // Group consecutive segments to form trapezoidal blocks
        const wallBlocks = this.groupConsecutiveWallSegments(segments);

        // Render each trapezoidal wall block from farthest to nearest
        for (let i = wallBlocks.length - 1; i >= 0; i--) {
            const block = wallBlocks[i];
            this.drawTrapezoidalSideWall(ctx, block, side);
        }
    }

    // Group consecutive wall segments into trapezoidal blocks
    groupConsecutiveWallSegments(segments) {
        if (segments.length === 0) return [];

        const blocks = [];
        let currentBlock = [segments[0]];

        for (let i = 1; i < segments.length; i++) {
            const prevSegment = segments[i - 1];
            const currentSegment = segments[i];

            // If segments are consecutive distances, add to current block
            if (currentSegment.distance === prevSegment.distance + 1) {
                currentBlock.push(currentSegment);
            } else {
                // Start a new block
                blocks.push(currentBlock);
                currentBlock = [currentSegment];
            }
        }

        // Add the last block
        blocks.push(currentBlock);
        return blocks;
    }

    // Draw a single trapezoidal side wall block
    drawTrapezoidalSideWall(ctx, wallBlock, side) {
        if (wallBlock.length === 0) return;

        // Get the near and far segments for trapezoid calculation
        const nearSegment = wallBlock[0]; // Closest to player (wider base)
        const farSegment = wallBlock[wallBlock.length - 1]; // Farthest from player (narrower top)

        // For consistent trapezoidal rendering, always create proper trapezoids
        // For single segments, calculate a synthetic far segment with reduced perspective
        let trapezoidCoords;
        let farSegmentData;

        if (wallBlock.length === 1) {
            // Single segment - create synthetic far segment for trapezoidal effect
            // Calculate a more pronounced perspective reduction to create enhanced depth
            const depthFactor = 0.7; // Reduce corridor width by 30% for enhanced depth effect
            const farCorridorWidth = nearSegment.corridorWidthAtDistance * depthFactor;
            const centerX = this.canvas.width / 2;
            const farLeftCorridorBoundary = centerX - farCorridorWidth / 2;
            const farRightCorridorBoundary = centerX + farCorridorWidth / 2;

            // Create synthetic far segment with more pronounced reduced dimensions
            const farWallHeight = nearSegment.wallHeight * depthFactor;
            // Adjust farWallY positioning to properly align the back edge of trapezoidal walls
            const farWallY = nearSegment.wallY + (nearSegment.wallHeight - farWallHeight) / 2;

            farSegmentData = {
                leftCorridorBoundary: farLeftCorridorBoundary,
                rightCorridorBoundary: farRightCorridorBoundary,
                wallY: farWallY,
                wallHeight: farWallHeight
            };
        } else {
            // Multiple segments - use actual far segment
            farSegmentData = farSegment;
        }

        // Create trapezoid coordinates using consistent logic for all cases
        if (side === 'left') {
            trapezoidCoords = {
                // Near edge (wider, closer to player)
                nearLeft: 0,
                nearRight: nearSegment.leftCorridorBoundary,
                nearTop: nearSegment.wallY,
                nearBottom: nearSegment.wallY + nearSegment.wallHeight,

                // Far edge (narrower, farther from player)
                farLeft: 0,
                farRight: farSegmentData.leftCorridorBoundary,
                farTop: farSegmentData.wallY,
                farBottom: farSegmentData.wallY + farSegmentData.wallHeight
            };
        } else {
            trapezoidCoords = {
                // Near edge (wider, closer to player)
                nearLeft: nearSegment.rightCorridorBoundary,
                nearRight: this.canvas.width,
                nearTop: nearSegment.wallY,
                nearBottom: nearSegment.wallY + nearSegment.wallHeight,

                // Far edge (narrower, farther from player)
                farLeft: farSegmentData.rightCorridorBoundary,
                farRight: this.canvas.width,
                farTop: farSegmentData.wallY,
                farBottom: farSegmentData.wallY + farSegmentData.wallHeight
            };
        }

        // Draw the trapezoidal wall surface
        this.drawTrapezoidalWallSurface(ctx, trapezoidCoords, nearSegment.perspective, side);
    }

    // Draw the actual trapezoidal wall surface with solid black fill and perspective effects
    drawTrapezoidalWallSurface(ctx, coords, perspective, side) {
        ctx.save();

        // Create and fill the trapezoidal path with solid black
        // Draw a proper trapezoid connecting near and far edges
        ctx.beginPath();
        ctx.moveTo(coords.nearLeft, coords.nearTop);      // Near top-left
        ctx.lineTo(coords.nearRight, coords.nearTop);     // Near top-right
        ctx.lineTo(coords.farRight, coords.farTop);       // Far top-right
        ctx.lineTo(coords.farLeft, coords.farTop);        // Far top-left
        ctx.lineTo(coords.farLeft, coords.farBottom);     // Far bottom-left
        ctx.lineTo(coords.farRight, coords.farBottom);    // Far bottom-right
        ctx.lineTo(coords.nearRight, coords.nearBottom);  // Near bottom-right
        ctx.lineTo(coords.nearLeft, coords.nearBottom);   // Near bottom-left
        ctx.closePath();

        // Fill with solid black for uniform side wall appearance
        ctx.fillStyle = '#000000';
        ctx.fill();

        ctx.restore();

        // Add perspective edges for depth
        this.drawTrapezoidalWallEdges(ctx, coords, perspective, side);

        // Add wall border for definition - but only draw the necessary edges
        ctx.strokeStyle = `rgba(${Math.floor(120 * perspective)}, ${Math.floor(100 * perspective)}, ${Math.floor(80 * perspective)}, 0.8)`;
        ctx.lineWidth = Math.max(1, 2 * perspective);

        // Only draw the edges that should be visible based on side
        ctx.beginPath();
        if (side === 'left') {
            // Near top horizontal edge
            ctx.moveTo(coords.nearLeft, coords.nearTop);
            ctx.lineTo(coords.nearRight, coords.nearTop);

            // Near bottom horizontal edge
            ctx.moveTo(coords.nearLeft, coords.nearBottom);
            ctx.lineTo(coords.nearRight, coords.nearBottom);

            // Left vertical edges (outer wall)
            ctx.moveTo(coords.nearLeft, coords.nearTop);
            ctx.lineTo(coords.nearLeft, coords.nearBottom);
            ctx.moveTo(coords.farLeft, coords.farTop);
            ctx.lineTo(coords.farLeft, coords.farBottom);

            // Right vertical edge (corridor boundary)
            ctx.moveTo(coords.nearRight, coords.nearTop);
            ctx.lineTo(coords.nearRight, coords.nearBottom);
            ctx.moveTo(coords.farRight, coords.farTop);
            ctx.lineTo(coords.farRight, coords.farBottom);
        } else {
            // Near top horizontal edge
            ctx.moveTo(coords.nearLeft, coords.nearTop);
            ctx.lineTo(coords.nearRight, coords.nearTop);

            // Near bottom horizontal edge
            ctx.moveTo(coords.nearLeft, coords.nearBottom);
            ctx.lineTo(coords.nearRight, coords.nearBottom);

            // Right vertical edges (outer wall)
            ctx.moveTo(coords.nearRight, coords.nearTop);
            ctx.lineTo(coords.nearRight, coords.nearBottom);
            ctx.moveTo(coords.farRight, coords.farTop);
            ctx.lineTo(coords.farRight, coords.farBottom);

            // Left vertical edge (corridor boundary)
            ctx.moveTo(coords.nearLeft, coords.nearTop);
            ctx.lineTo(coords.nearLeft, coords.nearBottom);
            ctx.moveTo(coords.farLeft, coords.farTop);
            ctx.lineTo(coords.farLeft, coords.farBottom);
        }
        ctx.stroke();
    }

    // Draw perspective edges for trapezoidal side walls
    drawTrapezoidalWallEdges(ctx, coords, perspective, side) {
        const edgeIntensity = Math.floor(80 * perspective);

        ctx.save();
        ctx.strokeStyle = `rgba(${edgeIntensity + 20}, ${edgeIntensity + 15}, ${edgeIntensity + 10}, 0.6)`;
        ctx.lineWidth = Math.max(1, perspective * 2);

        ctx.beginPath();
        if (side === 'left') {
            // Left side wall - draw converging lines toward center
            // Top edge convergence
            ctx.moveTo(coords.nearLeft, coords.nearTop);
            ctx.lineTo(coords.farLeft, coords.farTop);

            // Bottom edge convergence
            ctx.moveTo(coords.nearLeft, coords.nearBottom);
            ctx.lineTo(coords.farLeft, coords.farBottom);

            // Right edge (corridor boundary) convergence
            ctx.moveTo(coords.nearRight, coords.nearTop);
            ctx.lineTo(coords.farRight, coords.farTop);
            ctx.moveTo(coords.nearRight, coords.nearBottom);
            ctx.lineTo(coords.farRight, coords.farBottom);
        } else {
            // Right side wall - draw converging lines toward center
            // Top edge convergence
            ctx.moveTo(coords.nearRight, coords.nearTop);
            ctx.lineTo(coords.farRight, coords.farTop);

            // Bottom edge convergence
            ctx.moveTo(coords.nearRight, coords.nearBottom);
            ctx.lineTo(coords.farRight, coords.farBottom);

            // Left edge (corridor boundary) convergence
            ctx.moveTo(coords.nearLeft, coords.nearTop);
            ctx.lineTo(coords.farLeft, coords.farTop);
            ctx.moveTo(coords.nearLeft, coords.nearBottom);
            ctx.lineTo(coords.farLeft, coords.farBottom);
        }
        ctx.stroke();
        ctx.restore();
    }
    
    // Draw forward perspective view with 2-pass rendering
    drawForwardView(dir) {
        const canvas = this.canvas;
        const cellsToRender = [];

        // Calculate corridor width at screen edges (between side walls)
        const corridorWidth = canvas.width * 0.7; // 70% of screen width (from 15% to 85%)

        // Collect all cells to render
        for (let distance = 1; distance <= 5; distance++) {
            const checkX = gameState.player.x + (dir.x * distance);
            const checkY = gameState.player.y + (dir.y * distance);

            const cellInfo = this.getCellInfo(checkX, checkY);
            const perspective = 1 / (distance + 0.5);

            // Calculate wall dimensions that match corridor perspective
            const wallHeight = canvas.height * perspective * 0.8;
            // Use corridor width with perspective scaling for consistent alignment
            const wallWidth = corridorWidth * perspective;
            const wallY = (canvas.height - wallHeight) / 2;
            const wallX = (canvas.width - wallWidth) / 2;

            cellsToRender.push({
                x: wallX,
                y: wallY,
                width: wallWidth,
                height: wallHeight,
                perspective,
                cellInfo,
                distance
            });

            // Stop collecting cells behind walls
            if (cellInfo.isWall) {
                break;
            }
        }

        // Pass 1: Render background elements (walls)
        // Render walls and background content
        for (const cell of cellsToRender) {
            if (cell.cellInfo.isWall) {
                this.drawWall(cell.x, cell.y, cell.width, cell.height, cell.perspective);
            } else {
                // Draw non-enemy cell content in background pass
                this.drawBackgroundCellContent(cell.x, cell.y, cell.width, cell.height, cell.perspective, cell.cellInfo);
            }
        }

        // Pass 2: Render foreground elements (enemies and dynamic objects)
        for (const cell of cellsToRender) {
            if (!cell.cellInfo.isWall) {
                this.drawForegroundCellContent(cell.x, cell.y, cell.width, cell.height, cell.perspective, cell.cellInfo);
            }
        }
    }
    
    // Get cell information
    getCellInfo(x, y) {
        if (this.isOutOfBounds(x, y)) {
            return { isWall: true, type: 'wall', hasEnemy: false };
        }
        
        const cell = gameState.dungeon[y][x];
        return {
            isWall: cell.type === 'wall',
            type: cell.type,
            hasEnemy: cell.enemy
        };
    }
    
    // Check if position is out of bounds or wall
    isWallAt(x, y) {
        return this.isOutOfBounds(x, y) || gameState.dungeon[y][x].type === 'wall';
    }
    
    // Check if coordinates are out of bounds
    isOutOfBounds(x, y) {
        return x < 0 || x >= CONSTANTS.DUNGEON_SIZE || y < 0 || y >= CONSTANTS.DUNGEON_SIZE;
    }
    
    // Draw wall with solid fill and diagonal depth (no stone brick texture)
    drawWall(x, y, width, height, perspective) {
        const ctx = this.ctx;

        // Draw solid wall background
        const baseIntensity = Math.floor(85 * perspective);
        ctx.fillStyle = `rgb(${Math.floor(baseIntensity * 0.6)}, ${Math.floor(baseIntensity * 0.5)}, ${Math.floor(baseIntensity * 0.4)})`;
        ctx.fillRect(x, y, width, height);

        // Add diagonal perspective edges for depth
        this.drawWallPerspectiveEdges(ctx, x, y, width, height, perspective);

        // Add wall border for depth
        ctx.strokeStyle = `rgba(${Math.floor(120 * perspective)}, ${Math.floor(100 * perspective)}, ${Math.floor(80 * perspective)}, 0.8)`;
        ctx.lineWidth = Math.max(1, 2 * perspective);
        ctx.strokeRect(x, y, width, height);
    }
    
    // Draw diagonal perspective edges to create wall depth
    drawWallPerspectiveEdges(ctx, x, y, width, height, perspective) {
        const canvas = this.canvas;
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;
        
        // Calculate vanishing point and edge strengths
        const edgeIntensity = Math.floor(80 * perspective);
        const shadowIntensity = Math.floor(40 * perspective);
        
        ctx.save();
        
        // Draw angled top edge that converges toward vanishing point
        ctx.strokeStyle = `rgba(${edgeIntensity + 20}, ${edgeIntensity + 15}, ${edgeIntensity + 10}, 0.6)`;
        ctx.lineWidth = Math.max(1, perspective * 2);
        ctx.beginPath();
        
        // Top edge with slight inward angle
        const topLeftX = x;
        const topRightX = x + width;
        const topY = y;
        const angleOffset = width * 0.02; // Small diagonal offset
        
        ctx.moveTo(topLeftX, topY);
        ctx.lineTo(topLeftX + angleOffset, topY + height * 0.1);
        ctx.moveTo(topRightX, topY);
        ctx.lineTo(topRightX - angleOffset, topY + height * 0.1);
        ctx.stroke();
        
        // Draw angled bottom edge
        ctx.strokeStyle = `rgba(${shadowIntensity}, ${shadowIntensity}, ${shadowIntensity}, 0.8)`;
        ctx.beginPath();
        
        const bottomLeftX = x;
        const bottomRightX = x + width;
        const bottomY = y + height;
        
        ctx.moveTo(bottomLeftX, bottomY);
        ctx.lineTo(bottomLeftX + angleOffset, bottomY - height * 0.1);
        ctx.moveTo(bottomRightX, bottomY);
        ctx.lineTo(bottomRightX - angleOffset, bottomY - height * 0.1);
        ctx.stroke();
        
        // Add diagonal corner depth lines
        if (perspective > 0.4) {
            ctx.strokeStyle = `rgba(${edgeIntensity}, ${edgeIntensity - 10}, ${edgeIntensity - 20}, 0.4)`;
            ctx.lineWidth = Math.max(1, perspective * 1.5);
            
            // Top corners
            ctx.beginPath();
            ctx.moveTo(topLeftX, topY);
            ctx.lineTo(topLeftX + width * 0.05, topY + height * 0.05);
            ctx.moveTo(topRightX, topY);
            ctx.lineTo(topRightX - width * 0.05, topY + height * 0.05);
            ctx.stroke();
            
            // Bottom corners
            ctx.beginPath();
            ctx.moveTo(bottomLeftX, bottomY);
            ctx.lineTo(bottomLeftX + width * 0.05, bottomY - height * 0.05);
            ctx.moveTo(bottomRightX, bottomY);
            ctx.lineTo(bottomRightX - width * 0.05, bottomY - height * 0.05);
            ctx.stroke();
        }
        
        ctx.restore();
    }
    
    // Draw stone brick wall
    drawStoneBrickWall(ctx, x, y, width, height, perspective) {
        // Calculate brick dimensions based on perspective
        const brickWidth = Math.max(12, 24 * perspective);
        const brickHeight = Math.max(8, 16 * perspective);
        const mortarWidth = Math.max(1, 2 * perspective);
        
        // Base colors with perspective-based intensity
        const baseIntensity = Math.floor(85 * perspective);
        const lightIntensity = Math.floor(120 * perspective);
        const darkIntensity = Math.floor(60 * perspective);
        
        // Draw background mortar color
        ctx.fillStyle = `rgb(${Math.floor(baseIntensity * 0.6)}, ${Math.floor(baseIntensity * 0.5)}, ${Math.floor(baseIntensity * 0.4)})`;
        ctx.fillRect(x, y, width, height);
        
        // Draw individual bricks
        for (let row = 0; row * (brickHeight + mortarWidth) < height; row++) {
            const brickY = y + row * (brickHeight + mortarWidth);
            const offsetX = (row % 2) * (brickWidth / 2); // Offset every other row
            
            for (let col = 0; col * (brickWidth + mortarWidth) - offsetX < width + brickWidth; col++) {
                const brickX = x + col * (brickWidth + mortarWidth) - offsetX;
                
                // Skip if brick is completely outside bounds
                if (brickX + brickWidth < x || brickX > x + width || 
                    brickY + brickHeight < y || brickY > y + height) {
                    continue;
                }
                
                // Clip brick to wall bounds
                const clippedX = Math.max(brickX, x);
                const clippedY = Math.max(brickY, y);
                const clippedWidth = Math.min(brickX + brickWidth, x + width) - clippedX;
                const clippedHeight = Math.min(brickY + brickHeight, y + height) - clippedY;
                
                if (clippedWidth > 0 && clippedHeight > 0) {
                    this.drawSingleBrick(ctx, clippedX, clippedY, clippedWidth, clippedHeight, 
                                       baseIntensity, lightIntensity, darkIntensity, perspective);
                }
            }
        }
    }
    
    // Draw a single brick with realistic shading
    drawSingleBrick(ctx, x, y, width, height, baseIntensity, lightIntensity, darkIntensity, perspective) {
        // Add slight color variation for realism
        const variation = (Math.sin(x * 0.1) + Math.cos(y * 0.1)) * 10;
        const red = Math.max(0, Math.min(255, baseIntensity + variation));
        const green = Math.max(0, Math.min(255, baseIntensity * 0.8 + variation * 0.8));
        const blue = Math.max(0, Math.min(255, baseIntensity * 0.6 + variation * 0.6));
        
        // Main brick body
        ctx.fillStyle = `rgb(${Math.floor(red)}, ${Math.floor(green)}, ${Math.floor(blue)})`;
        ctx.fillRect(x, y, width, height);
        
        // Highlight on top and left edges
        const highlightWidth = Math.max(1, perspective * 2);
        ctx.fillStyle = `rgb(${Math.floor(lightIntensity)}, ${Math.floor(lightIntensity * 0.9)}, ${Math.floor(lightIntensity * 0.7)})`;
        ctx.fillRect(x, y, width, highlightWidth); // Top edge
        ctx.fillRect(x, y, highlightWidth, height); // Left edge
        
        // Shadow on bottom and right edges
        const shadowWidth = Math.max(1, perspective * 2);
        ctx.fillStyle = `rgb(${Math.floor(darkIntensity)}, ${Math.floor(darkIntensity * 0.8)}, ${Math.floor(darkIntensity * 0.6)})`;
        ctx.fillRect(x, y + height - shadowWidth, width, shadowWidth); // Bottom edge
        ctx.fillRect(x + width - shadowWidth, y, shadowWidth, height); // Right edge
        
        // Add subtle weathering marks if close enough
        if (perspective > 0.4) {
            this.addBrickWeathering(ctx, x, y, width, height, darkIntensity);
        }
    }
    
    // Add weathering effects to bricks
    addBrickWeathering(ctx, x, y, width, height, darkIntensity) {
        ctx.fillStyle = `rgba(${Math.floor(darkIntensity * 0.7)}, ${Math.floor(darkIntensity * 0.6)}, ${Math.floor(darkIntensity * 0.5)}, 0.3)`;
        
        // Random weathering spots
        const spots = Math.floor(Math.random() * 3) + 1;
        for (let i = 0; i < spots; i++) {
            const spotX = x + Math.random() * width * 0.8;
            const spotY = y + Math.random() * height * 0.8;
            const spotSize = Math.random() * 3 + 1;
            ctx.fillRect(spotX, spotY, spotSize, spotSize);
        }
    }
    
    // Ceiling rendering has been removed from the 3D corridor system


    

    






    // Ceiling rendering methods have been removed from the 3D corridor system

    // Ceiling beam rendering methods have been removed


    
    // Stone ceiling rendering has been removed
    
    // Ceiling beam rendering has been removed
    
    // Draw background cell content (static elements)
    drawBackgroundCellContent(x, y, width, height, perspective, cellInfo) {
        const ctx = this.ctx;
        
        // All interactive elements (stairs, doors, healing points) moved to foreground
        // Only structural elements remain in background pass
    }
    
    // Draw foreground cell content (dynamic elements like enemies)
    drawForegroundCellContent(x, y, width, height, perspective, cellInfo) {
        const ctx = this.ctx;
        
        // Draw stairs in foreground to prevent being hidden by walls
        if (cellInfo.type === 'stairs') {
            this.drawStairs(x, y, width, height, perspective);
        }
        
        // Draw doors in foreground to prevent being hidden by walls
        if (cellInfo.type === 'door') {
            this.drawDoor(x, y, width, height, perspective);
        }
        
        // Draw healing points in foreground to prevent being hidden by walls
        if (cellInfo.type === 'heal') {
            this.drawHealPoint(x, y, width, height, perspective);
        }
        
        // Draw boss (always in foreground)
        if (cellInfo.type === 'boss') {
            this.drawBoss(x, y, width, height, perspective);
        }
        
        // Draw enemy if present (and not boss)
        if (cellInfo.hasEnemy && cellInfo.type !== 'boss') {
            this.drawEnemy(x, y, width, height, perspective);
        }
    }
    
    // Draw cell content (stairs, doors, enemies, etc.) - Legacy method for compatibility
    drawCellContent(x, y, width, height, perspective, cellInfo) {
        this.drawBackgroundCellContent(x, y, width, height, perspective, cellInfo);
        this.drawForegroundCellContent(x, y, width, height, perspective, cellInfo);
    }
    
    // Draw stairs as a wooden ladder going down
    drawStairs(x, y, width, height, perspective) {
        const ctx = this.ctx;

        // Calculate ladder dimensions
        const ladderWidth = width * 0.6;
        const ladderHeight = height * 0.4;
        const ladderX = x + (width - ladderWidth) / 2;
        // Position ladder lower on screen by adding offset based on perspective
        // Closer ladders (higher perspective) get more offset for better visual placement
        const ladderOffset = height * 0.2 * perspective;
        const ladderY = y + height - ladderHeight + ladderOffset;

        // Draw the ladder going down with wooden appearance and gradients
        this.drawWoodenLadder(ctx, ladderX, ladderY, ladderWidth, ladderHeight, perspective);
    }
    
    // Draw a wooden ladder with gradients descending into the ground
    drawWoodenLadder(ctx, x, y, width, height, perspective) {
        const centerX = x + width / 2;

        // Wood color palette (adjusted for perspective/lighting)
        const lightWood = {
            r: Math.floor(139 * perspective),
            g: Math.floor(119 * perspective),
            b: Math.floor(101 * perspective)
        };
        const mediumWood = {
            r: Math.floor(101 * perspective),
            g: Math.floor(67 * perspective),
            b: Math.floor(33 * perspective)
        };
        const darkWood = {
            r: Math.floor(62 * perspective),
            g: Math.floor(39 * perspective),
            b: Math.floor(35 * perspective)
        };

        // Draw only the floating ladder rungs (no background opening or side rails)
        this.drawFloatingLadderRungs(ctx, x, y, width, height, lightWood, mediumWood, darkWood, perspective);
    }

    // Draw floating ladder rungs without side rails or background
    drawFloatingLadderRungs(ctx, x, y, width, height, lightWood, mediumWood, darkWood, perspective) {
        const rungCount = Math.floor(4 + (perspective * 3)); // More rungs when closer

        // Draw horizontal rungs only
        for (let i = 0; i < rungCount; i++) {
            const rungProgress = i / (rungCount - 1);
            const rungY = y + (height * 0.2) + (rungProgress * height * 0.6);
            const rungWidth = width * (0.6 - rungProgress * 0.1); // Narrower as it goes down
            const rungHeight = height * 0.05 * (1 - rungProgress * 0.3); // Thinner as it goes down
            const rungX = x + (width - rungWidth) / 2;

            this.drawLadderRung(ctx, rungX, rungY, rungWidth, rungHeight, lightWood, mediumWood, darkWood, rungProgress);
        }
    }

    // Draw the dark opening where the ladder descends
    drawLadderOpening(ctx, x, y, width, height, veryDarkWood) {
        // Create radial gradient for the opening depth effect
        const centerX = x + width / 2;
        const centerY = y + height / 2;
        const radius = Math.min(width, height) / 2;
        
        const gradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius);
        gradient.addColorStop(0, `rgb(${veryDarkWood.r}, ${veryDarkWood.g}, ${veryDarkWood.b})`);
        gradient.addColorStop(0.7, `rgb(${Math.floor(veryDarkWood.r * 1.5)}, ${Math.floor(veryDarkWood.g * 1.5)}, ${Math.floor(veryDarkWood.b * 1.5)})`);
        gradient.addColorStop(1, `rgb(${Math.floor(veryDarkWood.r * 2)}, ${Math.floor(veryDarkWood.g * 2)}, ${Math.floor(veryDarkWood.b * 2)})`);
        
        ctx.fillStyle = gradient;
        ctx.fillRect(x, y, width, height);
    }
    
    // Draw the wooden ladder structure with rails and rungs
    drawLadderStructure(ctx, x, y, width, height, lightWood, mediumWood, darkWood, perspective) {
        const railWidth = width * 0.08;
        const leftRailX = x + width * 0.2;
        const rightRailX = x + width * 0.72;
        const rungCount = Math.floor(4 + (perspective * 3)); // More rungs when closer
        
        // Draw left and right rails with vertical gradient
        this.drawLadderRail(ctx, leftRailX, y, railWidth, height, lightWood, mediumWood, darkWood, 'left');
        this.drawLadderRail(ctx, rightRailX, y, railWidth, height, lightWood, mediumWood, darkWood, 'right');
        
        // Draw horizontal rungs
        for (let i = 0; i < rungCount; i++) {
            const rungProgress = i / (rungCount - 1);
            const rungY = y + (height * 0.2) + (rungProgress * height * 0.6);
            const rungWidth = width * (0.6 - rungProgress * 0.1); // Narrower as it goes down
            const rungHeight = height * 0.05 * (1 - rungProgress * 0.3); // Thinner as it goes down
            const rungX = x + (width - rungWidth) / 2;
            
            this.drawLadderRung(ctx, rungX, rungY, rungWidth, rungHeight, lightWood, mediumWood, darkWood, rungProgress);
        }
    }
    
    // Draw a single ladder rail with gradient
    drawLadderRail(ctx, x, y, width, height, lightWood, mediumWood, darkWood, side) {
        // Create vertical gradient from light (top) to dark (bottom)
        const gradient = ctx.createLinearGradient(x, y, x, y + height);
        gradient.addColorStop(0, `rgb(${lightWood.r}, ${lightWood.g}, ${lightWood.b})`);
        gradient.addColorStop(0.3, `rgb(${mediumWood.r}, ${mediumWood.g}, ${mediumWood.b})`);
        gradient.addColorStop(0.7, `rgb(${darkWood.r}, ${darkWood.g}, ${darkWood.b})`);
        gradient.addColorStop(1, `rgb(${Math.floor(darkWood.r * 0.7)}, ${Math.floor(darkWood.g * 0.7)}, ${Math.floor(darkWood.b * 0.7)})`);
        
        ctx.fillStyle = gradient;
        ctx.fillRect(x, y, width, height);
        
        // Add wood texture highlight on the edge facing towards center
        const highlightX = side === 'left' ? x + width - 1 : x;
        const highlightGradient = ctx.createLinearGradient(highlightX, y, highlightX, y + height);
        highlightGradient.addColorStop(0, `rgba(${Math.floor(lightWood.r * 1.2)}, ${Math.floor(lightWood.g * 1.2)}, ${Math.floor(lightWood.b * 1.2)}, 0.6)`);
        highlightGradient.addColorStop(1, `rgba(${mediumWood.r}, ${mediumWood.g}, ${mediumWood.b}, 0.3)`);
        
        ctx.fillStyle = highlightGradient;
        ctx.fillRect(highlightX, y, 1, height);
    }
    
    // Draw a single ladder rung with gradient
    drawLadderRung(ctx, x, y, width, height, lightWood, mediumWood, darkWood, depthProgress) {
        // Create horizontal gradient for rung (lighter on top edge, darker on bottom)
        const topGradient = ctx.createLinearGradient(x, y, x, y + height);
        
        // Adjust colors based on depth (darker as it goes down)
        const depthFactor = 1 - (depthProgress * 0.4);
        const adjustedLight = {
            r: Math.floor(lightWood.r * depthFactor),
            g: Math.floor(lightWood.g * depthFactor),
            b: Math.floor(lightWood.b * depthFactor)
        };
        const adjustedMedium = {
            r: Math.floor(mediumWood.r * depthFactor),
            g: Math.floor(mediumWood.g * depthFactor),
            b: Math.floor(mediumWood.b * depthFactor)
        };
        const adjustedDark = {
            r: Math.floor(darkWood.r * depthFactor),
            g: Math.floor(darkWood.g * depthFactor),
            b: Math.floor(darkWood.b * depthFactor)
        };
        
        topGradient.addColorStop(0, `rgb(${adjustedLight.r}, ${adjustedLight.g}, ${adjustedLight.b})`);
        topGradient.addColorStop(0.4, `rgb(${adjustedMedium.r}, ${adjustedMedium.g}, ${adjustedMedium.b})`);
        topGradient.addColorStop(1, `rgb(${adjustedDark.r}, ${adjustedDark.g}, ${adjustedDark.b})`);
        
        ctx.fillStyle = topGradient;
        ctx.fillRect(x, y, width, height);
        
        // Add top highlight for 3D effect
        const highlightHeight = Math.max(1, height * 0.3);
        ctx.fillStyle = `rgba(${Math.floor(adjustedLight.r * 1.3)}, ${Math.floor(adjustedLight.g * 1.3)}, ${Math.floor(adjustedLight.b * 1.3)}, 0.8)`;
        ctx.fillRect(x, y, width, highlightHeight);
        
        // Add bottom shadow
        const shadowHeight = Math.max(1, height * 0.2);
        ctx.fillStyle = `rgba(${Math.floor(adjustedDark.r * 0.6)}, ${Math.floor(adjustedDark.g * 0.6)}, ${Math.floor(adjustedDark.b * 0.6)}, 0.8)`;
        ctx.fillRect(x, y + height - shadowHeight, width, shadowHeight);
    }
    
    // Draw door integrated into wall structure
    drawDoor(x, y, width, height, perspective) {
        const ctx = this.ctx;

        // Calculate realistic door proportions
        const doorWidth = width * 0.35; // More realistic door width
        const doorHeight = height * 0.85; // Leave space for lintel
        const doorX = x + (width - doorWidth) / 2;
        const doorY = y + height * 0.05; // Small offset from top for lintel

        // First, draw the wall with door cutout
        this.drawWallWithDoorCutout(ctx, x, y, width, height, doorX, doorY, doorWidth, doorHeight, perspective);

        // Then draw the recessed door within the cutout
        this.drawRecessedDoor(ctx, doorX, doorY, doorWidth, doorHeight, perspective);
    }

    // Draw wall with door cutout integrated
    drawWallWithDoorCutout(ctx, wallX, wallY, wallWidth, wallHeight, doorX, doorY, doorWidth, doorHeight, perspective) {
        // Draw wall sections around the door opening

        // Left wall section
        if (doorX > wallX) {
            this.drawStoneBrickWall(ctx, wallX, wallY, doorX - wallX, wallHeight, perspective);
        }

        // Right wall section
        const rightSectionX = doorX + doorWidth;
        if (rightSectionX < wallX + wallWidth) {
            this.drawStoneBrickWall(ctx, rightSectionX, wallY, wallX + wallWidth - rightSectionX, wallHeight, perspective);
        }

        // Top wall section (lintel)
        if (doorY > wallY) {
            this.drawStoneBrickWall(ctx, doorX, wallY, doorWidth, doorY - wallY, perspective);
        }

        // Bottom wall section (threshold)
        const bottomSectionY = doorY + doorHeight;
        if (bottomSectionY < wallY + wallHeight) {
            this.drawStoneBrickWall(ctx, doorX, bottomSectionY, doorWidth, wallY + wallHeight - bottomSectionY, perspective);
        }

        // Draw door frame/jamb around the opening
        this.drawDoorJamb(ctx, doorX, doorY, doorWidth, doorHeight, perspective);

        // Add wall perspective edges (but avoid door area)
        this.drawWallPerspectiveEdgesWithCutout(ctx, wallX, wallY, wallWidth, wallHeight, doorX, doorY, doorWidth, doorHeight, perspective);
    }

    // Draw door jamb (stone frame around door opening)
    drawDoorJamb(ctx, x, y, width, height, perspective) {
        const jambThickness = Math.max(2, 6 * perspective);
        const jambDepth = Math.max(1, 3 * perspective);

        // Stone jamb colors
        const stoneBase = Math.floor(75 * perspective);
        const stoneLight = Math.floor(95 * perspective);
        const stoneDark = Math.floor(55 * perspective);

        // Draw jamb sides (left and right)
        ctx.fillStyle = `rgb(${stoneBase}, ${Math.floor(stoneBase * 0.9)}, ${Math.floor(stoneBase * 0.8)})`;
        ctx.fillRect(x - jambThickness, y, jambThickness, height); // Left jamb
        ctx.fillRect(x + width, y, jambThickness, height); // Right jamb

        // Draw jamb top (lintel)
        ctx.fillRect(x - jambThickness, y - jambThickness, width + jambThickness * 2, jambThickness);

        // Add jamb highlights and shadows for depth
        ctx.fillStyle = `rgb(${stoneLight}, ${Math.floor(stoneLight * 0.9)}, ${Math.floor(stoneLight * 0.8)})`;
        ctx.fillRect(x - jambThickness, y, jambDepth, height); // Left highlight
        ctx.fillRect(x - jambThickness, y - jambThickness, width + jambThickness * 2, jambDepth); // Top highlight

        ctx.fillStyle = `rgb(${stoneDark}, ${Math.floor(stoneDark * 0.9)}, ${Math.floor(stoneDark * 0.8)})`;
        ctx.fillRect(x + width + jambThickness - jambDepth, y, jambDepth, height); // Right shadow
    }

    // Draw wall perspective edges avoiding door cutout area
    drawWallPerspectiveEdgesWithCutout(ctx, wallX, wallY, wallWidth, wallHeight, doorX, doorY, doorWidth, doorHeight, perspective) {
        const edgeIntensity = Math.floor(80 * perspective);
        const shadowIntensity = Math.floor(40 * perspective);

        ctx.save();

        // Draw perspective edges only on wall sections, not in door area
        ctx.strokeStyle = `rgba(${edgeIntensity + 20}, ${edgeIntensity + 15}, ${edgeIntensity + 10}, 0.6)`;
        ctx.lineWidth = Math.max(1, perspective * 2);

        // Top edge (avoiding door area)
        ctx.beginPath();
        if (doorX > wallX) {
            // Left section top edge
            ctx.moveTo(wallX, wallY);
            ctx.lineTo(doorX, wallY);
        }
        if (doorX + doorWidth < wallX + wallWidth) {
            // Right section top edge
            ctx.moveTo(doorX + doorWidth, wallY);
            ctx.lineTo(wallX + wallWidth, wallY);
        }
        ctx.stroke();

        // Bottom edge (avoiding door area)
        ctx.strokeStyle = `rgba(${shadowIntensity}, ${shadowIntensity}, ${shadowIntensity}, 0.8)`;
        ctx.beginPath();
        if (doorX > wallX) {
            // Left section bottom edge
            ctx.moveTo(wallX, wallY + wallHeight);
            ctx.lineTo(doorX, wallY + wallHeight);
        }
        if (doorX + doorWidth < wallX + wallWidth) {
            // Right section bottom edge
            ctx.moveTo(doorX + doorWidth, wallY + wallHeight);
            ctx.lineTo(wallX + wallWidth, wallY + wallHeight);
        }
        ctx.stroke();

        ctx.restore();
    }

    // Draw recessed door within the wall cutout
    drawRecessedDoor(ctx, x, y, width, height, perspective) {
        const recessDepth = Math.max(2, 8 * perspective);

        // Draw deep shadow in the door recess
        const recessShadow = Math.floor(15 * perspective);
        ctx.fillStyle = `rgb(${recessShadow}, ${recessShadow}, ${Math.floor(recessShadow * 0.9)})`;
        ctx.fillRect(x, y, width, height);

        // Calculate actual door position (slightly recessed)
        const doorInset = Math.max(1, 3 * perspective);
        const doorX = x + doorInset;
        const doorY = y + doorInset;
        const doorWidth = width - doorInset * 2;
        const doorHeight = height - doorInset * 2;

        // Draw wooden door surface
        this.drawWoodenDoorSurface(ctx, doorX, doorY, doorWidth, doorHeight, perspective);

        // Draw door details (handle, panels, etc.)
        this.drawDoorSurfaceDetails(ctx, doorX, doorY, doorWidth, doorHeight, perspective);
    }

    // Draw wooden door surface with grain texture
    drawWoodenDoorSurface(ctx, x, y, width, height, perspective) {
        // Base wood colors with perspective
        const baseWood = Math.floor(139 * perspective);
        const darkWood = Math.floor(101 * perspective);
        const lightWood = Math.floor(160 * perspective);

        // Create wood gradient
        const woodGradient = ctx.createLinearGradient(x, y, x + width, y);
        woodGradient.addColorStop(0, `rgb(${darkWood}, ${Math.floor(darkWood * 0.5)}, ${Math.floor(darkWood * 0.14)})`);
        woodGradient.addColorStop(0.3, `rgb(${baseWood}, ${Math.floor(baseWood * 0.5)}, ${Math.floor(baseWood * 0.14)})`);
        woodGradient.addColorStop(0.7, `rgb(${lightWood}, ${Math.floor(lightWood * 0.5)}, ${Math.floor(lightWood * 0.14)})`);
        woodGradient.addColorStop(1, `rgb(${darkWood}, ${Math.floor(darkWood * 0.5)}, ${Math.floor(darkWood * 0.14)})`);

        // Fill door with wood gradient
        ctx.fillStyle = woodGradient;
        ctx.fillRect(x, y, width, height);

        // Add wood grain texture
        this.drawWoodGrain(ctx, x, y, width, height, perspective);

        // Add door panels
        this.drawDoorPanels(ctx, x, y, width, height, perspective);
    }

    // Draw door surface details (handle, hinges, etc.)
    drawDoorSurfaceDetails(ctx, x, y, width, height, perspective) {
        if (perspective < 0.3) return; // Skip details for distant doors

        // Door handle position (right side, middle height)
        const handleX = x + width * 0.85;
        const handleY = y + height * 0.5;
        const handleSize = Math.max(2, 4 * perspective);

        // Handle shadow
        ctx.fillStyle = `rgb(20, 15, 10)`;
        ctx.beginPath();
        ctx.arc(handleX + 1, handleY + 1, handleSize, 0, 2 * Math.PI);
        ctx.fill();

        // Handle base (brass/bronze color)
        const handleBrass = Math.floor(180 * perspective);
        ctx.fillStyle = `rgb(${handleBrass}, ${Math.floor(handleBrass * 0.8)}, ${Math.floor(handleBrass * 0.3)})`;
        ctx.beginPath();
        ctx.arc(handleX, handleY, handleSize, 0, 2 * Math.PI);
        ctx.fill();

        // Handle highlight
        const handleHighlight = Math.floor(220 * perspective);
        ctx.fillStyle = `rgb(${handleHighlight}, ${Math.floor(handleHighlight * 0.9)}, ${Math.floor(handleHighlight * 0.5)})`;
        ctx.beginPath();
        ctx.arc(handleX - handleSize * 0.3, handleY - handleSize * 0.3, handleSize * 0.5, 0, 2 * Math.PI);
        ctx.fill();

        // Door hinges (left side) - only for close doors
        if (perspective > 0.5) {
            this.drawDoorHinges(ctx, x, y, width, height, perspective);
        }
    }

    // Legacy method for compatibility - now redirects to new system
    drawWoodenDoor(ctx, x, y, width, height, perspective) {
        this.drawWoodenDoorSurface(ctx, x, y, width, height, perspective);
    }

    // Draw wood grain texture
    drawWoodGrain(ctx, x, y, width, height, perspective) {
        if (perspective < 0.3) return; // Skip grain for distant doors

        const grainIntensity = Math.floor(80 * perspective);
        const grainSpacing = Math.max(3, 6 * perspective);

        ctx.save();
        ctx.globalAlpha = 0.3;

        // Vertical wood grain lines
        for (let i = 0; i < width; i += grainSpacing) {
            const grainVariation = Math.floor(Math.random() * 20) - 10;
            ctx.strokeStyle = `rgb(${grainIntensity + grainVariation}, ${Math.floor((grainIntensity + grainVariation) * 0.5)}, ${Math.floor((grainIntensity + grainVariation) * 0.14)})`;
            ctx.lineWidth = Math.max(0.5, perspective);

            ctx.beginPath();
            ctx.moveTo(x + i, y);
            ctx.lineTo(x + i, y + height);
            ctx.stroke();
        }

        // Horizontal wood grain (subtle)
        for (let i = 0; i < height; i += grainSpacing * 3) {
            const grainVariation = Math.floor(Math.random() * 15) - 7;
            ctx.strokeStyle = `rgb(${grainIntensity + grainVariation}, ${Math.floor((grainIntensity + grainVariation) * 0.5)}, ${Math.floor((grainIntensity + grainVariation) * 0.14)})`;
            ctx.lineWidth = Math.max(0.3, perspective * 0.7);

            ctx.beginPath();
            ctx.moveTo(x, y + i);
            ctx.lineTo(x + width, y + i);
            ctx.stroke();
        }

        ctx.restore();
    }

    // Draw door panels for depth
    drawDoorPanels(ctx, x, y, width, height, perspective) {
        const panelMargin = Math.max(2, 4 * perspective);
        const panelSeparation = Math.max(1, 2 * perspective);

        // Upper panel
        const upperPanelHeight = (height - panelMargin * 3 - panelSeparation) * 0.4;
        this.drawSinglePanel(ctx, x + panelMargin, y + panelMargin, width - panelMargin * 2, upperPanelHeight, perspective);

        // Lower panel
        const lowerPanelY = y + panelMargin + upperPanelHeight + panelSeparation;
        const lowerPanelHeight = height - panelMargin * 2 - upperPanelHeight - panelSeparation;
        this.drawSinglePanel(ctx, x + panelMargin, lowerPanelY, width - panelMargin * 2, lowerPanelHeight, perspective);
    }

    // Draw individual door panel with beveled edges
    drawSinglePanel(ctx, x, y, width, height, perspective) {
        const bevelSize = Math.max(1, 2 * perspective);
        const shadowIntensity = Math.floor(40 * perspective);
        const highlightIntensity = Math.floor(120 * perspective);

        // Panel shadow (recessed look)
        ctx.fillStyle = `rgb(${shadowIntensity}, ${Math.floor(shadowIntensity * 0.5)}, ${Math.floor(shadowIntensity * 0.14)})`;
        ctx.fillRect(x, y, width, height);

        // Panel highlight (top and left edges)
        ctx.fillStyle = `rgb(${highlightIntensity}, ${Math.floor(highlightIntensity * 0.5)}, ${Math.floor(highlightIntensity * 0.14)})`;
        ctx.fillRect(x, y, width, bevelSize); // Top edge
        ctx.fillRect(x, y, bevelSize, height); // Left edge

        // Panel shadow (bottom and right edges)
        ctx.fillStyle = `rgb(${shadowIntensity}, ${Math.floor(shadowIntensity * 0.5)}, ${Math.floor(shadowIntensity * 0.14)})`;
        ctx.fillRect(x, y + height - bevelSize, width, bevelSize); // Bottom edge
        ctx.fillRect(x + width - bevelSize, y, bevelSize, height); // Right edge
    }



    // Draw door hinges
    drawDoorHinges(ctx, x, y, width, height, perspective) {
        const hingeWidth = Math.max(1, 2 * perspective);
        const hingeHeight = Math.max(3, 6 * perspective);
        const hingeX = x - hingeWidth;

        // Upper hinge
        const upperHingeY = y + height * 0.2;
        this.drawSingleHinge(ctx, hingeX, upperHingeY, hingeWidth, hingeHeight, perspective);

        // Lower hinge
        const lowerHingeY = y + height * 0.8 - hingeHeight;
        this.drawSingleHinge(ctx, hingeX, lowerHingeY, hingeWidth, hingeHeight, perspective);
    }

    // Draw individual hinge
    drawSingleHinge(ctx, x, y, width, height, perspective) {
        const metalColor = Math.floor(60 * perspective);
        const metalHighlight = Math.floor(100 * perspective);

        // Hinge base
        ctx.fillStyle = `rgb(${metalColor}, ${metalColor}, ${metalColor})`;
        ctx.fillRect(x, y, width, height);

        // Hinge highlight
        ctx.fillStyle = `rgb(${metalHighlight}, ${metalHighlight}, ${metalHighlight})`;
        ctx.fillRect(x, y, width * 0.5, height * 0.3);
    }
    
    // Draw healing point with ethereal design
    drawHealPoint(x, y, width, height, perspective) {
        const ctx = this.ctx;
        const healSize = width * 0.6; // Larger size for better visibility
        const healY = y + height - healSize;
        const healX = x + (width - healSize) / 2;
        
        this.drawEtherealHealPoint(ctx, healX, healY, healSize, perspective);
    }
    
    // Draw ethereal healing point with circular blurred design
    drawEtherealHealPoint(ctx, x, y, size, perspective) {
        const centerX = x + size / 2;
        const centerY = y + size / 2;
        const radius = size / 2;

        // Animation based on time - use performance.now() for better precision
        const time = (performance.now() || Date.now()) * 0.001; // Slower animation for healing
        const pulse = Math.sin(time * 2) * 0.15 + 1; // Gentle pulsing
        const flicker = Math.sin(time * 1.5) * 0.1 + 0.9; // Soft flickering
        const rotationAngle = time * 0.8; // Slow rotation for light ring
        
        // Save context for effects
        ctx.save();
        
        // Outer ethereal glow (largest, most transparent)
        const outerRadius = radius * 1.8 * pulse;
        const outerGradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, outerRadius);
        outerGradient.addColorStop(0, `rgba(255, 255, 255, ${0.4 * perspective * flicker})`);
        outerGradient.addColorStop(0.3, `rgba(200, 255, 200, ${0.25 * perspective})`);
        outerGradient.addColorStop(0.7, `rgba(100, 255, 150, ${0.1 * perspective})`);
        outerGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
        
        ctx.fillStyle = outerGradient;
        ctx.beginPath();
        ctx.arc(centerX, centerY, outerRadius, 0, 2 * Math.PI);
        ctx.fill();
        
        // Middle healing aura
        const middleRadius = radius * 1.3 * pulse;
        const middleGradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, middleRadius);
        middleGradient.addColorStop(0, `rgba(255, 220, 100, ${0.6 * perspective * flicker})`);
        middleGradient.addColorStop(0.4, `rgba(100, 255, 150, ${0.5 * perspective})`);
        middleGradient.addColorStop(0.8, `rgba(200, 255, 200, ${0.2 * perspective})`);
        middleGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
        
        ctx.fillStyle = middleGradient;
        ctx.beginPath();
        ctx.arc(centerX, centerY, middleRadius, 0, 2 * Math.PI);
        ctx.fill();
        
        // Core healing light
        const coreRadius = radius * 0.9 * pulse;
        const coreGradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, coreRadius);
        coreGradient.addColorStop(0, `rgba(255, 240, 120, ${0.8 * perspective * flicker})`);
        coreGradient.addColorStop(0.3, `rgba(255, 220, 100, ${0.7 * perspective})`);
        coreGradient.addColorStop(0.6, `rgba(150, 255, 180, ${0.5 * perspective})`);
        coreGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
        
        ctx.fillStyle = coreGradient;
        ctx.beginPath();
        ctx.arc(centerX, centerY, coreRadius, 0, 2 * Math.PI);
        ctx.fill();
        
        // Inner bright core
        const innerRadius = radius * 0.4 * pulse;
        ctx.fillStyle = `rgba(255, 255, 200, ${0.9 * perspective * flicker})`;
        ctx.beginPath();
        ctx.arc(centerX, centerY, innerRadius, 0, 2 * Math.PI);
        ctx.fill();
        
        // Add rotating light ring if perspective is good enough
        if (perspective > 0.4) {
            this.drawHealingLightRing(ctx, centerX, centerY, radius, rotationAngle, perspective, flicker);
        }
        
        // Add floating healing particles
        if (perspective > 0.3) {
            this.drawHealingParticles(ctx, centerX, centerY, radius, time, perspective);
        }
        
        ctx.restore();
    }
    
    // Draw rotating light ring around healing point
    drawHealingLightRing(ctx, centerX, centerY, radius, rotationAngle, perspective, flicker) {
        const ringRadius = radius * 1.5;
        const lightCount = 8;
        
        for (let i = 0; i < lightCount; i++) {
            const angle = rotationAngle + (i * Math.PI * 2 / lightCount);
            const lightX = centerX + Math.cos(angle) * ringRadius;
            const lightY = centerY + Math.sin(angle) * ringRadius;
            const lightSize = 3 * perspective;
            
            // Create small glowing orbs
            const orbGradient = ctx.createRadialGradient(lightX, lightY, 0, lightX, lightY, lightSize * 2);
            orbGradient.addColorStop(0, `rgba(255, 255, 150, ${0.8 * perspective * flicker})`);
            orbGradient.addColorStop(0.5, `rgba(200, 255, 200, ${0.4 * perspective})`);
            orbGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
            
            ctx.fillStyle = orbGradient;
            ctx.beginPath();
            ctx.arc(lightX, lightY, lightSize * 2, 0, 2 * Math.PI);
            ctx.fill();
        }
    }
    
    // Draw floating healing particles
    drawHealingParticles(ctx, centerX, centerY, radius, time, perspective) {
        const particleCount = 12;
        
        for (let i = 0; i < particleCount; i++) {
            // Create upward floating motion
            const baseHeight = radius * 2;
            const floatHeight = (time * 20 + i * 15) % (baseHeight * 1.5);
            const particleY = centerY + radius - floatHeight;
            
            // Slight horizontal drift
            const driftX = Math.sin(time * 0.5 + i) * radius * 0.3;
            const particleX = centerX + driftX;
            
            // Fade out as particles rise
            const fadeAlpha = Math.max(0, 1 - (floatHeight / (baseHeight * 1.5)));
            const particleSize = (2 + Math.sin(time * 3 + i)) * perspective;
            
            if (fadeAlpha > 0) {
                const particleGradient = ctx.createRadialGradient(
                    particleX, particleY, 0, 
                    particleX, particleY, particleSize * 2
                );
                particleGradient.addColorStop(0, `rgba(255, 255, 200, ${0.6 * perspective * fadeAlpha})`);
                particleGradient.addColorStop(0.5, `rgba(200, 255, 180, ${0.3 * perspective * fadeAlpha})`);
                particleGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
                
                ctx.fillStyle = particleGradient;
                ctx.beginPath();
                ctx.arc(particleX, particleY, particleSize * 2, 0, 2 * Math.PI);
                ctx.fill();
            }
        }
    }
    
    // Draw boss with enhanced ethereal design
    drawBoss(x, y, width, height, perspective) {
        const ctx = this.ctx;
        const bossSize = Math.min(width, height) * 0.8;
        const bossX = x + (width - bossSize) / 2;
        const bossY = y + (height - bossSize) / 2;
        
        // Draw ethereal boss symbol
        this.drawEtherealSymbol(ctx, bossX, bossY, bossSize, perspective, 'boss');
    }
    
    // Draw enemy with ethereal circular design
    drawEnemy(x, y, width, height, perspective) {
        const ctx = this.ctx;
        const enemySize = Math.min(width, height) * 0.6;
        const enemyX = x + (width - enemySize) / 2;
        const enemyY = y + (height - enemySize) / 2;
        
        // Draw ethereal enemy symbol
        this.drawEtherealSymbol(ctx, enemyX, enemyY, enemySize, perspective, 'enemy');
    }
    
    // Draw ethereal symbol for enemies and bosses
    drawEtherealSymbol(ctx, x, y, size, perspective, type) {
        const centerX = x + size / 2;
        const centerY = y + size / 2;
        const radius = size / 2;

        // Animation based on time - use performance.now() for better precision
        const time = (performance.now() || Date.now()) * 0.002;
        const pulse = Math.sin(time) * 0.1 + 1;
        const flicker = Math.sin(time * 3) * 0.1 + 0.9;
        
        // Set colors based on type
        let primaryColor, secondaryColor, glowColor;
        if (type === 'boss') {
            primaryColor = `rgba(200, 100, 255, ${0.8 * perspective * flicker})`;
            secondaryColor = `rgba(150, 50, 200, ${0.6 * perspective})`;
            glowColor = `rgba(255, 150, 255, ${0.4 * perspective})`;
        } else {
            primaryColor = `rgba(100, 150, 255, ${0.7 * perspective * flicker})`;
            secondaryColor = `rgba(50, 100, 200, ${0.5 * perspective})`;
            glowColor = `rgba(150, 200, 255, ${0.3 * perspective})`;
        }
        
        // Save context for blur effects
        ctx.save();
        
        // Create outer glow/halo effect
        const outerRadius = radius * 1.4 * pulse;
        const gradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, outerRadius);
        gradient.addColorStop(0, glowColor);
        gradient.addColorStop(0.7, `rgba(255, 255, 255, ${0.1 * perspective})`);
        gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
        
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(centerX, centerY, outerRadius, 0, 2 * Math.PI);
        ctx.fill();
        
        // Create middle ethereal ring
        const middleRadius = radius * 1.1 * pulse;
        const middleGradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, middleRadius);
        middleGradient.addColorStop(0, secondaryColor);
        middleGradient.addColorStop(0.8, `rgba(255, 255, 255, ${0.2 * perspective})`);
        middleGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
        
        ctx.fillStyle = middleGradient;
        ctx.beginPath();
        ctx.arc(centerX, centerY, middleRadius, 0, 2 * Math.PI);
        ctx.fill();
        
        // Create core ethereal circle
        const coreRadius = radius * 0.8 * pulse;
        const coreGradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, coreRadius);
        coreGradient.addColorStop(0, primaryColor);
        coreGradient.addColorStop(0.6, secondaryColor);
        coreGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
        
        ctx.fillStyle = coreGradient;
        ctx.beginPath();
        ctx.arc(centerX, centerY, coreRadius, 0, 2 * Math.PI);
        ctx.fill();
        
        // Add inner bright core
        const innerRadius = radius * 0.3 * pulse;
        ctx.fillStyle = `rgba(255, 255, 255, ${0.6 * perspective * flicker})`;
        ctx.beginPath();
        ctx.arc(centerX, centerY, innerRadius, 0, 2 * Math.PI);
        ctx.fill();
        
        // Add floating particles effect for boss
        if (type === 'boss' && perspective > 0.3) {
            this.drawEtherealParticles(ctx, centerX, centerY, radius, time, perspective);
        }
        
        ctx.restore();
    }
    
    // Draw floating particles around boss
    drawEtherealParticles(ctx, centerX, centerY, radius, time, perspective) {
        const particleCount = 6;
        const orbitRadius = radius * 1.5;

        for (let i = 0; i < particleCount; i++) {
            const angle = (time * 0.5) + (i * Math.PI * 2 / particleCount);
            const particleX = centerX + Math.cos(angle) * orbitRadius;
            const particleY = centerY + Math.sin(angle) * orbitRadius;
            const particleSize = 2 * perspective;

            const alpha = (Math.sin(time * 2 + i) + 1) * 0.25 * perspective;
            if (alpha > 0) {
                ctx.fillStyle = `rgba(255, 200, 255, ${alpha})`;
                ctx.beginPath();
                ctx.arc(particleX, particleY, particleSize, 0, 2 * Math.PI);
                ctx.fill();
            }
        }
    }
    
    
    // Add wall texture lines
    addWallTexture(x, y, width, height) {
        const ctx = this.ctx;
        ctx.strokeStyle = '#2a2a3e';
        ctx.lineWidth = 1;
        for (let i = 0; i < height; i += 20) {
            ctx.beginPath();
            ctx.moveTo(x, y + i);
            ctx.lineTo(x + width, y + i);
            ctx.stroke();
        }
    }

    // Draw atmospheric effects (floating particles, etc.)
    drawAtmosphericEffects() {
        // Check if atmospheric effects are enabled
        if (CONSTANTS.ATMOSPHERIC_EFFECTS && CONSTANTS.ATMOSPHERIC_EFFECTS.ENABLED) {
            // Draw floating particles rising from the dungeon floor
            this.drawFloatingParticles();
        }
    }

    // Draw floating particles that rise from the dungeon floor
    drawFloatingParticles() {
        const ctx = this.ctx;
        const canvas = this.canvas;

        // Get current time for animation
        const time = (performance.now() || Date.now()) * 0.001; // Convert to seconds

        // Enhanced particle configuration for better visibility and coverage
        const particleCount = CONSTANTS.ATMOSPHERIC_EFFECTS?.PARTICLE_COUNT || 45; // Use config or fallback to 45
        const corridorWidth = canvas.width * 0.8; // Expanded from 0.7 to 0.8 for wider coverage

        ctx.save();

        // Generate particles across the visible corridor area
        for (let i = 0; i < particleCount; i++) {
            // Create pseudo-random but consistent particle positions based on time and index
            const particleTime = time * 0.3 + i * 0.8; // Slower movement, staggered timing
            const particlePhase = (particleTime + i * 2.5) % 10; // 10-second cycle per particle

            // Calculate particle depth (distance from player) - distribute across 3-grid visibility
            const depth = 1 + (i % 3) + (particlePhase * 0.3) % 2; // 1-5 grid distance
            const perspective = 1 / (depth + 0.5); // Match perspective calculation from drawForwardView

            // Skip particles that are too far or too faint
            if (perspective < 0.12) continue; // Reduced threshold from 0.15 to 0.12 for more distant particles

            // Calculate corridor boundaries at this depth with expanded coverage
            const corridorWidthAtDepth = corridorWidth * perspective;
            const corridorLeftAtDepth = (canvas.width - corridorWidthAtDepth) / 2;
            const corridorRightAtDepth = corridorLeftAtDepth + corridorWidthAtDepth;

            // Enhanced particle horizontal position with wider distribution
            const horizontalOffset = Math.sin(time * 0.4 + i * 1.3) * 0.4; // Increased from 0.3 to 0.4 for more drift
            const horizontalSpread = (i % 7) / 6 - 0.5; // Distribute particles across corridor width
            const particleX = corridorLeftAtDepth + (corridorWidthAtDepth * 0.5) +
                            (horizontalOffset * corridorWidthAtDepth * 0.5) +
                            (horizontalSpread * corridorWidthAtDepth * 0.8);

            // Ensure particle stays within expanded corridor bounds
            if (particleX < corridorLeftAtDepth - corridorWidthAtDepth * 0.1 ||
                particleX > corridorRightAtDepth + corridorWidthAtDepth * 0.1) continue;

            // Enhanced particle vertical movement with increased rise height
            const floorLevel = canvas.height * 0.85; // Floor is at 85% of screen height
            const riseHeight = canvas.height * 0.7 * perspective; // Increased from 0.6 to 0.7 for higher rise
            const verticalProgress = (particlePhase % 5) / 5; // 5-second rise cycle
            const particleY = floorLevel - (verticalProgress * riseHeight);

            // Allow particles to rise higher for better coverage
            if (particleY < canvas.height * 0.15) continue; // Reduced from 0.2 to 0.15

            // Enhanced particle size for maximum visibility
            const baseSize = 4.5 * perspective; // Increased from 3.0 to 4.5 for larger particles
            const sizeVariation = Math.sin(time * 2 + i) * 0.4 + 1; // Increased variation from 0.3 to 0.4
            const particleSize = baseSize * sizeVariation;

            // Enhanced opacity for better visibility
            const distanceFade = perspective; // Fade with distance
            const heightFade = Math.max(0, 1 - verticalProgress); // Fade as it rises
            const alpha = distanceFade * heightFade * 0.85; // Increased from 0.7 to 0.85 for higher visibility

            // Skip particles that are too faint
            if (alpha < 0.015) continue; // Reduced threshold from 0.02 to 0.015

            // Enhanced warm earth tone colors with more vibrant appearance
            const colorVariation = Math.sin(time * 0.5 + i * 0.7) * 0.25 + 0.85; // Increased variation
            const red = Math.floor(200 * colorVariation);   // Increased from 180 to 200
            const green = Math.floor(140 * colorVariation); // Increased from 120 to 140
            const blue = Math.floor(80 * colorVariation);   // Increased from 60 to 80

            // Draw particle with enhanced radial gradient for soft appearance
            const gradient = ctx.createRadialGradient(
                particleX, particleY, 0,
                particleX, particleY, particleSize * 2.5 // Increased gradient radius from 2 to 2.5
            );
            gradient.addColorStop(0, `rgba(${red}, ${green}, ${blue}, ${alpha})`);
            gradient.addColorStop(0.4, `rgba(${red}, ${green}, ${blue}, ${alpha * 0.7})`); // Adjusted stop from 0.5 to 0.4
            gradient.addColorStop(0.8, `rgba(${red}, ${green}, ${blue}, ${alpha * 0.3})`); // Added intermediate stop
            gradient.addColorStop(1, `rgba(${red}, ${green}, ${blue}, 0)`);

            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(particleX, particleY, particleSize * 2.5, 0, 2 * Math.PI); // Increased radius from 2 to 2.5
            ctx.fill();
        }

        ctx.restore();
    }

    // Add scan line effect
    addScanLines() {
        const ctx = this.ctx;
        const canvas = this.canvas;
        ctx.fillStyle = 'rgba(0, 255, 65, 0.1)';
        for (let i = 0; i < canvas.height; i += 4) {
            ctx.fillRect(0, i, canvas.width, 1);
        }
    }
    
    // Update minimap
    updateMinimap() {
        const grid = document.getElementById('minimapGrid');
        if (!grid) return;
        
        // Clear grid first
        grid.innerHTML = '';
        
        // Only show minimap if mapping tool has been obtained
        if (!gameState.hasMappingTool) {
            // Show message that minimap is unavailable
            const messageDiv = document.createElement('div');
            messageDiv.className = 'minimap-message';
            messageDiv.textContent = 'DUNGEON MAP is unavailable. The Mapping Tool is required';
            messageDiv.style.cssText = `
                display: flex;
                align-items: center;
                justify-content: center;
                height: 100%;
                text-align: center;
                color: #666;
                font-size: 12px;
                white-space: pre-line;
            `;
            grid.appendChild(messageDiv);
            return;
        }
        
        // Show normal minimap when tool is available
        for (let y = 0; y < CONSTANTS.DUNGEON_SIZE; y++) {
            for (let x = 0; x < CONSTANTS.DUNGEON_SIZE; x++) {
                const cell = document.createElement('div');
                cell.className = 'minimap-cell';
                
                const cellData = gameState.dungeon[y][x];
                
                // Check if this position should show a shop
                const shopInfo = this.getShopAtPosition(x, y);
                
                if (shopInfo) {
                    // Show shop if door is visited or adjacent cells are visited
                    if (this.shouldShowShop(shopInfo.doorX, shopInfo.doorY)) {
                        if (shopInfo.shopType === 'priest') {
                            cell.className += ' shop-chapel';
                        } else if (shopInfo.shopType === 'alchemist') {
                            cell.className += ' shop-alchemist';
                        } else {
                            cell.className += ' shop';
                        }
                    }
                } else if (cellData.visited || (x === gameState.player.x && y === gameState.player.y)) {
                    this.setMinimapCellClass(cell, cellData);
                    
                    if (cellData.enemy && cellData.type !== 'boss') {
                        cell.className += ' enemy';
                    }
                }
                
                if (x === gameState.player.x && y === gameState.player.y) {
                    cell.className += ' player';
                }
                
                grid.appendChild(cell);
            }
        }
    }
    
    // Get shop information at a specific position
    getShopAtPosition(x, y) {
        // Check all doors in the dungeon to see if any have shop positions at this location
        for (let doorY = 0; doorY < CONSTANTS.DUNGEON_SIZE; doorY++) {
            for (let doorX = 0; doorX < CONSTANTS.DUNGEON_SIZE; doorX++) {
                const doorCell = gameState.dungeon[doorY][doorX];
                if (doorCell.type === 'door' && doorCell.shopType && doorCell.shopPosition) {
                    if (doorCell.shopPosition.x === x && doorCell.shopPosition.y === y) {
                        return {
                            doorX: doorX,
                            doorY: doorY,
                            shopType: doorCell.shopType
                        };
                    }
                }
            }
        }
        return null;
    }
    
    // Check if a shop should be shown based on door visibility
    shouldShowShop(doorX, doorY) {
        const doorCell = gameState.dungeon[doorY][doorX];
        
        // Show shop if the door itself is visited
        if (doorCell.visited) {
            return true;
        }
        
        // Show shop if any adjacent cell to the door is visited
        const directions = [
            { dx: 0, dy: -1 }, // North
            { dx: 1, dy: 0 },  // East
            { dx: 0, dy: 1 },  // South
            { dx: -1, dy: 0 }  // West
        ];
        
        for (const dir of directions) {
            const adjX = doorX + dir.dx;
            const adjY = doorY + dir.dy;
            
            if (adjX >= 0 && adjX < CONSTANTS.DUNGEON_SIZE && adjY >= 0 && adjY < CONSTANTS.DUNGEON_SIZE) {
                if (gameState.dungeon[adjY][adjX].visited) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    // Set minimap cell class based on cell type
    setMinimapCellClass(cell, cellData) {
        switch (cellData.type) {
            case 'wall':
                cell.className += ' wall';
                break;
            case 'floor':
                cell.className += ' visited';
                break;
            case 'stairs':
                cell.className += ' stairs';
                break;
            case 'door':
                cell.className += ' door';
                break;
            case 'heal':
                cell.className += ' heal';
                break;
            case 'boss':
                cell.className += ' boss';
                break;
        }
    }
}

// Create global instance
const renderer = new Renderer();

// Make available globally for browser
if (typeof window !== 'undefined') {
    window.renderer = renderer;
}
