// ===== SMOKE SCREEN ACQUISITION NOTIFICATION =====
// Displays an overlay when a smoke screen is obtained

class SmokeScreenNotification {
    constructor() {
        this.modal = null;
        this.isShowing = false;
        this.imageLoaded = false;
        this.init();
    }
    
    init() {
        // Get the modal element
        this.modal = document.getElementById('smokeScreenModal');
        if (!this.modal) {
            console.error('Smoke screen modal not found in DOM');
            return;
        }
        
        // Set up click handler to dismiss the notification
        this.modal.addEventListener('click', () => {
            this.dismissNotification();
        });
        
        // Preload the smoke screen image
        this.preloadImage();
        
        console.log('Smoke screen notification system initialized');
    }
    
    // Preload the smoke screen image
    async preloadImage() {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => {
                this.imageLoaded = true;
                console.log('Smoke screen image preloaded successfully');
                resolve();
            };
            img.onerror = () => {
                console.error('Failed to preload smoke screen image');
                reject();
            };
            img.src = 'assets/images/smoke screen.png';
        });
    }
    
    // Show the smoke screen notification
    async showNotification() {
        if (!this.modal || this.isShowing) return;
        
        console.log('Showing smoke screen notification');
        
        // Ensure the image is loaded before showing
        if (!this.imageLoaded) {
            await this.preloadImage();
        }
        
        // Show the modal
        this.modal.classList.add('active');
        this.isShowing = true;
        
        // Add a slight delay for better visual effect
        setTimeout(() => {
            if (this.modal) {
                this.modal.style.opacity = '1';
            }
        }, 50);
        
        // Auto-dismiss after 5 seconds if user doesn't click
        setTimeout(() => {
            if (this.isShowing) {
                this.dismissNotification();
            }
        }, 5000);
    }
    
    // Dismiss the notification
    dismissNotification() {
        if (!this.modal || !this.isShowing) return;
        
        console.log('Dismissing smoke screen notification');
        
        // Fade out effect
        this.modal.style.opacity = '0';
        
        // Hide the modal after fade out
        setTimeout(() => {
            if (this.modal) {
                this.modal.classList.remove('active');
                this.isShowing = false;
            }
        }, 300);
    }
    
    // Check if notification is currently showing
    isNotificationShowing() {
        return this.isShowing;
    }

    // Debug method to test the notification
    testNotification() {
        console.log('Testing smoke screen notification...');
        this.showNotification();
    }
}

// Create global instance
const smokeScreenNotification = new SmokeScreenNotification();

// Make available globally for browser
if (typeof window !== 'undefined') {
    window.smokeScreenNotification = smokeScreenNotification;

    // Add global test function for debugging
    window.testSmokeScreenOverlay = function() {
        console.log('Testing smoke screen overlay...');
        if (window.smokeScreenNotification) {
            window.smokeScreenNotification.testNotification();
        } else {
            console.error('Smoke screen notification system not available');
        }
    };

    // Add test function to simulate purchasing a smoke screen
    window.testSmokeScreenPurchase = function() {
        console.log('Testing smoke screen purchase...');
        if (window.gameState) {
            window.gameState.addItem('smoke_screen', 1);
        } else {
            console.error('Game state not available');
        }
    };
}
