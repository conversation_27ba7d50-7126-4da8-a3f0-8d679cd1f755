// ===== OPTION SCREEN =====
// Manages the option screen display and fullscreen functionality

class OptionScreen {
    constructor() {
        this.isShowing = false;
        this.optionScreenElement = null;
        this.isFullscreen = false;
        this.originalCanvasStyle = null;
        this.originalGameContainerStyle = null;
    }
    
    // Initialize the option screen
    initialize() {
        console.log('OptionScreen: Initializing...');
        this.createOptionScreenHTML();
        this.setupFullscreenListeners();
    }
    
    // Create the option screen HTML structure
    createOptionScreenHTML() {
        // Create main option screen container
        const optionScreen = document.createElement('div');
        optionScreen.className = 'option-screen';
        optionScreen.id = 'optionScreen';
        
        // Create option content container
        const optionContent = document.createElement('div');
        optionContent.className = 'option-content';
        
        // Create title
        const title = document.createElement('div');
        title.className = 'option-title';
        title.textContent = 'OPTIONS';
        
        // Create subtitle
        const subtitle = document.createElement('div');
        subtitle.className = 'option-subtitle';
        subtitle.textContent = 'Configure your game settings';
        
        // Create screen mode section
        const screenSection = document.createElement('div');
        screenSection.className = 'option-section';
        
        const screenTitle = document.createElement('div');
        screenTitle.className = 'option-section-title';
        screenTitle.textContent = 'Screen Mode';
        
        const screenButtons = document.createElement('div');
        screenButtons.className = 'option-buttons';
        
        // Windowed mode button
        const windowedButton = document.createElement('button');
        windowedButton.className = 'option-button';
        windowedButton.id = 'windowedModeButton';
        windowedButton.textContent = 'Windowed';
        windowedButton.onclick = () => this.setWindowedMode();
        
        // Fullscreen mode button
        const fullscreenButton = document.createElement('button');
        fullscreenButton.className = 'option-button';
        fullscreenButton.id = 'fullscreenModeButton';
        fullscreenButton.textContent = 'Fullscreen';
        fullscreenButton.onclick = () => this.setFullscreenMode();
        
        screenButtons.appendChild(windowedButton);
        screenButtons.appendChild(fullscreenButton);
        
        screenSection.appendChild(screenTitle);
        screenSection.appendChild(screenButtons);
        
        // Create back button
        const backButton = document.createElement('button');
        backButton.className = 'option-back-button';
        backButton.textContent = 'BACK';
        backButton.onclick = () => this.returnToTitleScreen();
        
        // Assemble the option screen
        optionContent.appendChild(title);
        optionContent.appendChild(subtitle);
        optionContent.appendChild(screenSection);
        optionContent.appendChild(backButton);
        
        optionScreen.appendChild(optionContent);
        
        // Add to document body
        document.body.appendChild(optionScreen);
        this.optionScreenElement = optionScreen;
        
        // Add keyboard event listener for ESC key
        this.keyboardHandler = (event) => {
            if (event.key === 'Escape' && this.isShowing) {
                this.returnToTitleScreen();
            }
        };
        
        console.log('OptionScreen: HTML structure created and added to DOM');
    }
    
    // Setup fullscreen event listeners
    setupFullscreenListeners() {
        // Listen for fullscreen change events
        document.addEventListener('fullscreenchange', () => this.onFullscreenChange());
        document.addEventListener('webkitfullscreenchange', () => this.onFullscreenChange());
        document.addEventListener('mozfullscreenchange', () => this.onFullscreenChange());
        document.addEventListener('MSFullscreenChange', () => this.onFullscreenChange());

        // Listen for window resize events to adjust layout
        window.addEventListener('resize', () => {
            if (this.isInFullscreen()) {
                setTimeout(() => this.adjustGameLayout(), 100);
            }
        });
    }
    
    // Show the option screen
    show() {
        console.log('OptionScreen: Attempting to show option screen...');
        if (!this.optionScreenElement) {
            this.initialize();
        }

        if (this.optionScreenElement) {
            this.optionScreenElement.style.display = 'flex';
            this.optionScreenElement.classList.remove('fade-out');
            this.isShowing = true;

            // Update button states after a short delay to ensure DOM is ready
            setTimeout(() => {
                this.updateButtonStates();
            }, 50);

            console.log('OptionScreen: Option screen displayed successfully');

            // Add keyboard event listener
            document.addEventListener('keydown', this.keyboardHandler);
        } else {
            console.error('OptionScreen: optionScreenElement is null!');
        }
    }
    
    // Hide the option screen
    hide() {
        if (this.optionScreenElement) {
            this.optionScreenElement.style.display = 'none';
            this.optionScreenElement.classList.remove('fade-out');
            this.isShowing = false;
            console.log('Option screen hidden');
        }
        
        // Remove keyboard event listener
        document.removeEventListener('keydown', this.keyboardHandler);
    }
    
    // Return to title screen
    returnToTitleScreen() {
        console.log('OptionScreen: Returning to title screen...');

        // Hide option screen
        this.hide();

        // Show title screen using its proper return method
        if (typeof titleScreen !== 'undefined' && titleScreen.returnFromOptionScreen) {
            titleScreen.returnFromOptionScreen();
        } else {
            console.error('TitleScreen not available or missing returnFromOptionScreen method');
        }
    }
    
    // Set windowed mode
    setWindowedMode() {
        console.log('OptionScreen: Setting windowed mode...');
        
        if (this.isInFullscreen()) {
            this.exitFullscreen();
        }
        
        this.updateButtonStates();
    }
    
    // Set fullscreen mode
    setFullscreenMode() {
        console.log('OptionScreen: Setting fullscreen mode...');
        
        if (!this.isInFullscreen()) {
            this.enterFullscreen();
        }
        
        this.updateButtonStates();
    }
    
    // Enter fullscreen mode
    enterFullscreen() {
        const element = document.documentElement;
        
        if (element.requestFullscreen) {
            element.requestFullscreen();
        } else if (element.webkitRequestFullscreen) {
            element.webkitRequestFullscreen();
        } else if (element.mozRequestFullScreen) {
            element.mozRequestFullScreen();
        } else if (element.msRequestFullscreen) {
            element.msRequestFullscreen();
        }
    }
    
    // Exit fullscreen mode
    exitFullscreen() {
        if (document.exitFullscreen) {
            document.exitFullscreen();
        } else if (document.webkitExitFullscreen) {
            document.webkitExitFullscreen();
        } else if (document.mozCancelFullScreen) {
            document.mozCancelFullScreen();
        } else if (document.msExitFullscreen) {
            document.msExitFullscreen();
        }
    }
    
    // Check if currently in fullscreen
    isInFullscreen() {
        return !!(document.fullscreenElement || 
                 document.webkitFullscreenElement || 
                 document.mozFullScreenElement || 
                 document.msFullscreenElement);
    }
    
    // Handle fullscreen change events
    onFullscreenChange() {
        const isFullscreen = this.isInFullscreen();
        console.log('OptionScreen: Fullscreen state changed:', isFullscreen);
        
        this.isFullscreen = isFullscreen;
        this.updateButtonStates();
        this.adjustGameLayout();
    }
    
    // Update button states based on current mode
    updateButtonStates() {
        const windowedButton = document.getElementById('windowedModeButton');
        const fullscreenButton = document.getElementById('fullscreenModeButton');
        
        if (windowedButton && fullscreenButton) {
            if (this.isInFullscreen()) {
                windowedButton.classList.remove('active');
                fullscreenButton.classList.add('active');
            } else {
                windowedButton.classList.add('active');
                fullscreenButton.classList.remove('active');
            }
        }
    }
    
    // Adjust game layout for fullscreen/windowed mode
    adjustGameLayout() {
        const canvas = document.getElementById('gameCanvas');
        const gameContainer = document.querySelector('.game-container');
        
        if (!canvas || !gameContainer) return;
        
        if (this.isInFullscreen()) {
            // Store original styles
            if (!this.originalCanvasStyle) {
                this.originalCanvasStyle = {
                    width: canvas.style.width,
                    height: canvas.style.height,
                    maxWidth: canvas.style.maxWidth,
                    maxHeight: canvas.style.maxHeight
                };
            }
            
            if (!this.originalGameContainerStyle) {
                this.originalGameContainerStyle = {
                    width: gameContainer.style.width,
                    height: gameContainer.style.height,
                    maxWidth: gameContainer.style.maxWidth,
                    maxHeight: gameContainer.style.maxHeight
                };
            }
            
            // Apply fullscreen styles
            gameContainer.style.width = '100vw';
            gameContainer.style.height = '100vh';
            gameContainer.style.maxWidth = 'none';
            gameContainer.style.maxHeight = 'none';
            
            // Scale canvas to fit screen while maintaining aspect ratio
            const aspectRatio = 800 / 600;
            const screenWidth = window.innerWidth;
            const screenHeight = window.innerHeight;
            const screenAspectRatio = screenWidth / screenHeight;
            
            if (screenAspectRatio > aspectRatio) {
                // Screen is wider than canvas aspect ratio
                canvas.style.height = '100vh';
                canvas.style.width = `${screenHeight * aspectRatio}px`;
            } else {
                // Screen is taller than canvas aspect ratio
                canvas.style.width = '100vw';
                canvas.style.height = `${screenWidth / aspectRatio}px`;
            }
            
            canvas.style.maxWidth = 'none';
            canvas.style.maxHeight = 'none';
            
        } else {
            // Restore original styles
            if (this.originalCanvasStyle) {
                canvas.style.width = this.originalCanvasStyle.width;
                canvas.style.height = this.originalCanvasStyle.height;
                canvas.style.maxWidth = this.originalCanvasStyle.maxWidth;
                canvas.style.maxHeight = this.originalCanvasStyle.maxHeight;
            }
            
            if (this.originalGameContainerStyle) {
                gameContainer.style.width = this.originalGameContainerStyle.width;
                gameContainer.style.height = this.originalGameContainerStyle.height;
                gameContainer.style.maxWidth = this.originalGameContainerStyle.maxWidth;
                gameContainer.style.maxHeight = this.originalGameContainerStyle.maxHeight;
            }
        }
        
        // Trigger a render update if renderer is available
        if (typeof renderer !== 'undefined' && renderer.render) {
            setTimeout(() => renderer.render(), 100);
        }
    }
    
    // Check if option screen is currently showing
    isCurrentlyShowing() {
        return this.isShowing;
    }
}

// Create global instance
const optionScreen = new OptionScreen();

// Make available globally for browser
if (typeof window !== 'undefined') {
    window.optionScreen = optionScreen;
}
