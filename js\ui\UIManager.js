// ===== UI MANAGER =====
// User interface updates and display management

class UIManager {
    constructor() {
        this.initialized = false;
    }
    
    // Defer event listener setup until everything is loaded
    initialize() {
        if (this.initialized) return;
        
        this.setupEventListeners();
        this.updateUI();
        
        // Setup game over detection
        gameState.addEventListener(CONSTANTS.EVENTS.GAME_STATE_CHANGED, () => {
            if (gameState.isGameOver()) {
                setTimeout(() => this.showGameOver(), 500);
            }
        });
        
        this.initialized = true;
    }
    
    // Setup event listeners for automatic UI updates
    setupEventListeners() {
        if (!gameState) return; // Safety check
        
        gameState.addEventListener(CONSTANTS.EVENTS.GAME_STATE_CHANGED, () => {
            this.updateUI();
        });
        
        gameState.addEventListener(CONSTANTS.EVENTS.PLAYER_MOVED, () => {
            this.updateUI();
            if (renderer) renderer.render();
        });
        
        gameState.addEventListener(CONSTANTS.EVENTS.COMBAT_STARTED, () => {
            this.updateCombatVisibility(true);
        });
        
        gameState.addEventListener(CONSTANTS.EVENTS.COMBAT_ENDED, () => {
            this.updateCombatVisibility(false);
        });
        
        gameState.addEventListener(CONSTANTS.EVENTS.FLOOR_CHANGED, () => {
            this.updateUI();
            if (renderer) renderer.render();
        });

        // Initialize mapping tool notification system
        if (typeof mappingToolNotification !== 'undefined') {
            mappingToolNotification.initialize();
        }

        // Initialize health potion notification system
        if (typeof healthPotionNotification !== 'undefined') {
            console.log('Health potion notification system available and ready');
        }

        // Initialize smoke screen notification system
        if (typeof smokeScreenNotification !== 'undefined') {
            console.log('Smoke screen notification system available and ready');
        }

        // Initialize floor progression notification system
        if (typeof floorProgressionNotification !== 'undefined') {
            console.log('Floor progression notification system available and ready');
        }

        // Initialize wild meat notification system
        if (typeof wildMeatNotification !== 'undefined') {
            console.log('Wild meat notification system available and ready');
        }
    }
    
    // Main UI update method
    updateUI() {
        if (!gameState || !gameState.party) return; // Safety check
        
        this.updatePartyDisplay();
        this.updateGameStats();
        this.updateFloorInfo();
        this.updateActionButtons();
        if (renderer) renderer.updateMinimap();
    }
    
    // Update party member display
    updatePartyDisplay() {
        const partyDiv = document.getElementById('partyMembers');
        if (!partyDiv) return;
        
        partyDiv.innerHTML = '';
        
        gameState.party.forEach(member => {
            const div = document.createElement('div');
            div.className = 'character' + (member.life <= 0 ? ' dead' : '');

            const healthPercent = (member.life / member.maxLife) * 100;

            // Check for status effects
            let statusEffects = '';
            if (member.poisoned && member.poisonTurnsLeft > 0) {
                statusEffects += ' <span style="color: #66ff66;">💚 POISONED</span>';
            }
            if (member.protected && member.protectedTurnsLeft > 0) {
                statusEffects += ' <span style="color: #87CEEB;">🛡️ PROTECTED</span>';
            }
            if (member.swiftMovement && member.swiftMovementTurnsLeft > 0) {
                statusEffects += ' <span style="color: #00FF7F;">💨 SWIFT</span>';
            }

            div.innerHTML = `
                <div class="char-name">${member.name} (Lv.${member.level})${statusEffects}</div>
                <div class="health-bar">
                    <div class="health-fill" style="width: ${healthPercent}%"></div>
                </div>
                <div class="char-stats">
                    LIFE: ${member.life}/${member.maxLife} | STR: ${member.str} | INT: ${member.int} | AGL: ${member.agl}
                </div>
                <div class="char-stats">EXP: ${member.exp}/${member.expToNext}</div>
            `;

            partyDiv.appendChild(div);
        });
    }
    
    // Update game statistics display
    updateGameStats() {
        this.updateElement('turnCounter', gameState.turnCounter);
        this.updateElement('enemiesDefeated', gameState.enemiesDefeated);
        this.updateElement('stepsTaken', gameState.stepsTaken);
        this.updateElement('respawnTimer', gameState.enemyRespawnTimer);
        this.updateElement('partyGold', gameState.gold);
    }
    
    // Update floor information
    updateFloorInfo() {
        this.updateElement('currentFloor', gameState.currentFloor);
        this.updateElement('dungeonDepth', gameState.getDepthName());
        this.updateElement('playerX', gameState.player.x);
        this.updateElement('playerY', gameState.player.y);
    }
    
    // Update action buttons visibility
    updateActionButtons() {
        const currentCell = gameState.getCurrentCell();
        const facingCell = gameState.getFacingCell();
        
        this.updateButtonVisibility('descendBtn', currentCell.type === 'stairs');
        this.updateButtonVisibility('doorBtn', facingCell && facingCell.type === 'door' && !facingCell.opened);
        this.updateButtonVisibility('healBtn', currentCell.type === 'heal');
    }
    
    // Update combat UI visibility
    updateCombatVisibility(visible) {
        const combatUI = document.getElementById('combatUI');
        if (combatUI) {
            if (visible) {
                combatUI.classList.add('active');
            } else {
                combatUI.classList.remove('active');
            }
        }
    }
    
    // Helper method to update text content of an element
    updateElement(id, value) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
        }
    }
    
    // Helper method to update button visibility
    updateButtonVisibility(id, visible) {
        const button = document.getElementById(id);
        if (button) {
            button.style.display = visible ? 'block' : 'none';
        }
    }
    
    // Show game over screen
    showGameOver() {
        const gameOverScreen = document.getElementById('gameOver');
        if (gameOverScreen) {
            gameOverScreen.classList.add('active');
        }
    }
    
    // Show victory screen
    showVictory() {
        const victoryScreen = document.getElementById('victoryScreen');
        if (victoryScreen) {
            // Update victory message based on game completion status
            const victoryContent = victoryScreen.querySelector('.victory-content');
            if (victoryContent) {
                if (gameState.isGameCompleted()) {
                    // Game completion message
                    victoryContent.innerHTML = `
                        <h2>ULTIMATE VICTORY!</h2>
                        <p>You have conquered the dungeon!</p>
                        <p>The Ancient Evil has been vanquished!</p>
                        <p>The world is saved from eternal darkness!</p>
                        <p style="color: gold; font-weight: bold;">🎉 GAME COMPLETED! 🎉</p>
                        <button class="action-btn" onclick="location.reload()">Play Again</button>
                    `;
                } else {
                    // Regular boss victory message
                    victoryContent.innerHTML = `
                        <h2>VICTORY!</h2>
                        <p>You have conquered the dungeon!</p>
                        <p>The Dark Lord has been defeated!</p>
                        <button class="action-btn" onclick="location.reload()">Play Again</button>
                    `;
                }
            }
            victoryScreen.classList.add('active');
        }
    }
    
    // Show level up modal
    showLevelUpModal() {
        const modal = document.getElementById('levelUpModal');
        if (modal) {
            modal.classList.add('active');
        }
    }
    
    // Hide level up modal
    hideLevelUpModal() {
        const modal = document.getElementById('levelUpModal');
        if (modal) {
            modal.classList.remove('active');
        }
    }
    
    // Show door interaction UI
    showDoorUI() {
        const doorUI = document.getElementById('doorUI');
        if (doorUI) {
            doorUI.classList.add('active');
        }
    }
    
    // Hide door interaction UI
    hideDoorUI() {
        const doorUI = document.getElementById('doorUI');
        if (doorUI) {
            doorUI.classList.remove('active');
        }
    }
    
    // Update combat log
    updateCombatLog(messages) {
        const logDiv = document.getElementById('combatLog');
        if (!logDiv) return;
        
        logDiv.innerHTML = '';
        
        messages.slice(-10).forEach(entry => {
            const div = document.createElement('div');
            div.className = `log-entry log-${entry.type}`;
            div.textContent = entry.message;
            logDiv.appendChild(div);
        });
        
        logDiv.scrollTop = logDiv.scrollHeight;
    }
    
    // Add message to combat log
    addCombatMessage(message, type = 'system') {
        // This will be called by the combat system
        const logDiv = document.getElementById('combatLog');
        if (!logDiv) return;
        
        const div = document.createElement('div');
        div.className = `log-entry log-${type}`;
        div.textContent = message;
        logDiv.appendChild(div);
        
        // Keep only last 10 messages
        const entries = logDiv.getElementsByClassName('log-entry');
        while (entries.length > 10) {
            logDiv.removeChild(entries[0]);
        }
        
        logDiv.scrollTop = logDiv.scrollHeight;
    }
}

// Create global instance
const uiManager = new UIManager();

// Make available globally for browser
if (typeof window !== 'undefined') {
    window.uiManager = uiManager;
}
