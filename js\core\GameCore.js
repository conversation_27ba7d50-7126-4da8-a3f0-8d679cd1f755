// ===== GAME CORE =====
// Main game initialization, loop, and coordination

class GameCore {
    constructor() {
        this.initialized = false;
        this.gameLoopId = null;
        this.titleScreenShown = false;
        this.gameStarted = false;
    }
    
    // Initialize the game
    async init() {
        if (this.initialized) return;

        try {
            // Setup canvas
            const canvas = document.getElementById('gameCanvas');
            if (!canvas) {
                throw new Error('Game canvas not found');
            }

            // Initialize renderer
            renderer.initialize(canvas);

            // Create party
            characterSystem.createParty();

            // Set initial gold for testing shops
            gameState.gold = 2000; // Increased for mapping tool testing

            // Generate all dungeon floors
            const floors = dungeonGenerator.generateAllFloors();
            gameState.setFloors(floors);

            // Set initial dungeon and mark starting position as visited
            gameState.setDungeon(floors[0]);
            gameState.dungeon[gameState.player.y][gameState.player.x].visited = true;

            // Preload images silently in background
            console.log('Preloading game images...');
            const imageResults = await imageManager.preloadAllImages();

            // Print diagnostic report to console only
            if (imageResults.failed > 0) {
                console.warn(`⚠️ ${imageResults.failed} images failed to load. Generating diagnostic report...`);
                imageManager.printDiagnosticReport();
            } else {
                console.log('✅ All images loaded successfully!');
            }

            // Show title screen immediately after loading
            this.showTitleScreen();

            // Initialize UI (but don't show it yet)
            uiManager.initialize();

            this.initialized = true;
            console.log('Game initialized successfully - showing title screen');

        } catch (error) {
            console.error('Game initialization failed:', error);
            this.showInitializationError(error);
        }
    }
    
    // Start the main game loop
    startGameLoop() {
        const gameLoop = () => {
            this.update();
            this.gameLoopId = requestAnimationFrame(gameLoop);
        };
        
        gameLoop();
    }
    
    // Main game update loop
    update() {
        // Check for game over condition
        if (gameState.isGameOver()) {
            this.handleGameOver();
            return;
        }

        // Update any time-based systems here
        // Currently most updates are event-driven

        // Update renderer for continuous atmospheric effects and animations
        if (renderer && this.gameStarted) {
            // Check atmospheric effects configuration
            const shouldRender = CONSTANTS.ATMOSPHERIC_EFFECTS.CONTINUOUS_RENDERING &&
                               ((CONSTANTS.ATMOSPHERIC_EFFECTS.RENDER_DURING_EXPLORATION && !gameState.inCombat) ||
                                (CONSTANTS.ATMOSPHERIC_EFFECTS.RENDER_IN_COMBAT && gameState.inCombat));

            if (shouldRender) {
                // Always render to maintain atmospheric effects (floating particles)
                // This ensures particles are continuously animated during exploration
                renderer.render();
            }
        }
    }

    // Check if there are animated elements visible in the current view
    hasVisibleAnimatedElements() {
        const dir = gameState.getCurrentDirectionVector();

        // Check cells in forward view for animated elements
        for (let distance = 1; distance <= 5; distance++) {
            const checkX = gameState.player.x + (dir.x * distance);
            const checkY = gameState.player.y + (dir.y * distance);

            if (checkX >= 0 && checkX < CONSTANTS.DUNGEON_SIZE &&
                checkY >= 0 && checkY < CONSTANTS.DUNGEON_SIZE) {
                const cell = gameState.dungeon[checkY][checkX];

                if (cell.type === 'heal' || cell.type === 'boss' || cell.enemy) {
                    return true;
                }

                // Stop checking if we hit a wall
                if (cell.type === 'wall') {
                    break;
                }
            }
        }

        return false;
    }
    
    // Handle game over
    handleGameOver() {
        if (this.gameLoopId) {
            cancelAnimationFrame(this.gameLoopId);
            this.gameLoopId = null;
        }
        
        // Game over screen is handled by UIManager
        console.log('Game Over');
    }
    
    // Handle victory
    handleVictory() {
        if (this.gameLoopId) {
            cancelAnimationFrame(this.gameLoopId);
            this.gameLoopId = null;
        }
        
        uiManager.showVictory();
        console.log('Victory!');
    }
    
    // Restart the game
    restart() {
        // Stop current game loop
        if (this.gameLoopId) {
            cancelAnimationFrame(this.gameLoopId);
            this.gameLoopId = null;
        }
        
        // Reset game state
        this.resetGameState();
        
        // Reinitialize
        this.init();
    }
    
    // Reset all game systems to initial state
    resetGameState() {
        // Reset player position
        gameState.setPlayerPosition(CONSTANTS.PLAYER_START_X, CONSTANTS.PLAYER_START_Y);
        gameState.setPlayerDirection(CONSTANTS.DIRECTIONS.NORTH);
        
        // Reset game statistics
        gameState.currentFloor = 1;
        gameState.turnCounter = 0;
        gameState.enemiesDefeated = 0;
        gameState.stepsTaken = 0;
        gameState.enemyRespawnTimer = CONSTANTS.ENEMY.RESPAWN_TIMER;
        gameState.inCombat = false;
        gameState.gold = 0;

        // Reset boss progression flags
        gameState.darkLordDefeated = false;
        gameState.ancientEvilSpawned = false;
        gameState.gameCompleted = false;
        
        // Clear party
        gameState.party = [];
        
        // Clear dungeon data
        gameState.dungeon = [];
        gameState.floors = [];
        
        // Hide all modals
        this.hideAllModals();
        
        this.initialized = false;
    }
    
    // Hide all game modals
    hideAllModals() {
        const modals = [
            'combatUI',
            'levelUpModal',
            'doorUI',
            'shopUI',
            'partySelectionUI',
            'gameOver',
            'victoryScreen'
        ];
        
        modals.forEach(modalId => {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('active');
            }
        });
    }
    
    // Show initialization error
    showInitializationError(error) {
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #ff0000;
            color: white;
            padding: 20px;
            border-radius: 10px;
            z-index: 9999;
            font-family: 'Orbitron', monospace;
        `;
        errorDiv.innerHTML = `
            <h2>Game Initialization Error</h2>
            <p>${error.message}</p>
            <button onclick="location.reload()">Reload Page</button>
        `;
        document.body.appendChild(errorDiv);
    }



    // Show title screen
    showTitleScreen() {
        console.log('GameCore: Attempting to show title screen...');
        if (typeof titleScreen !== 'undefined') {
            console.log('GameCore: TitleScreen class found, initializing...');
            titleScreen.initialize();
            titleScreen.show();
            this.titleScreenShown = true;
            console.log('GameCore: Title screen displayed');
        } else {
            console.warn('GameCore: TitleScreen not available, starting game directly');
            this.startMainGame();
        }
    }

    // Called when title screen START button is clicked
    onTitleScreenComplete() {
        console.log('Title screen completed, starting main game');
        this.startMainGame();
    }

    // Start the main game (called after title screen)
    startMainGame() {
        if (this.gameStarted) return;

        try {
            // Start game loop
            this.startGameLoop();

            // Initial render
            renderer.render();

            // Verify animation systems
            this.verifyAnimationSystems();

            // Start main game background music
            this.startMainGameMusic();

            this.gameStarted = true;
            console.log('Main game started successfully');

        } catch (error) {
            console.error('Failed to start main game:', error);
        }
    }

    // Start main game background music
    async startMainGameMusic() {
        if (typeof soundManager !== 'undefined' && soundManager.playFloorBasedBackgroundMusic) {
            try {
                console.log('GameCore: Starting floor-based main game background music...');

                // Set up floor change event listener
                if (soundManager.setupFloorChangeListener) {
                    soundManager.setupFloorChangeListener();
                }

                // Start floor-appropriate background music
                const result = await soundManager.playFloorBasedBackgroundMusic();

                if (result.success) {
                    console.log('GameCore: Floor-based main game background music started successfully');
                } else {
                    console.warn('GameCore: Failed to start floor-based main game background music:', result.error);
                }
            } catch (error) {
                console.error('GameCore: Error starting floor-based main game background music:', error);
            }
        } else {
            console.warn('GameCore: SoundManager not available or does not support floor-based background music');
        }
    }

    // Verify that animation systems are working
    verifyAnimationSystems() {
        console.log('Verifying animation systems...');

        // Check if requestAnimationFrame is supported
        if (typeof requestAnimationFrame === 'undefined') {
            console.warn('requestAnimationFrame not supported - animations may not work');
        } else {
            console.log('✓ requestAnimationFrame supported');
        }

        // Check if performance.now is supported
        if (typeof performance !== 'undefined' && typeof performance.now === 'function') {
            console.log('✓ performance.now() supported for high-precision timing');
        } else {
            console.log('⚠ performance.now() not supported, falling back to Date.now()');
        }

        // Check CSS animation support
        const testElement = document.createElement('div');
        testElement.style.animation = 'test 1s';
        if (testElement.style.animation) {
            console.log('✓ CSS animations supported');
        } else {
            console.warn('⚠ CSS animations may not be supported');
        }

        // Check if game loop is running
        if (this.gameLoopId) {
            console.log('✓ Game loop is running');
        } else {
            console.warn('⚠ Game loop is not running');
        }

        // Verify atmospheric effects configuration
        if (CONSTANTS.ATMOSPHERIC_EFFECTS && CONSTANTS.ATMOSPHERIC_EFFECTS.ENABLED) {
            console.log('✓ Atmospheric effects enabled');
            console.log(`✓ Particle count: ${CONSTANTS.ATMOSPHERIC_EFFECTS.PARTICLE_COUNT}`);
            console.log(`✓ Continuous rendering: ${CONSTANTS.ATMOSPHERIC_EFFECTS.CONTINUOUS_RENDERING}`);
        } else {
            console.warn('⚠ Atmospheric effects disabled or not configured');
        }

        console.log('Animation system verification completed');
    }

    // Pause/resume game
    pause() {
        if (this.gameLoopId) {
            cancelAnimationFrame(this.gameLoopId);
            this.gameLoopId = null;
        }
    }

    resume() {
        if (!this.gameLoopId && this.initialized && this.gameStarted) {
            this.startGameLoop();
        }
    }
    
    // Get game statistics
    getGameStats() {
        return {
            initialized: this.initialized,
            floor: gameState.currentFloor,
            turns: gameState.turnCounter,
            enemiesDefeated: gameState.enemiesDefeated,
            steps: gameState.stepsTaken,
            partyStats: characterSystem.getPartyStats()
        };
    }
    
    // Debug methods
    debug() {
        return {
            gameState: gameState,
            player: player,
            combat: combatSystem,
            character: characterSystem,
            dungeon: dungeonGenerator,
            renderer: renderer,
            ui: uiManager,
            input: inputHandler,
            stats: this.getGameStats()
        };
    }
}

// Create global instance
const gameCore = new GameCore();

// Make available globally for browser
if (typeof window !== 'undefined') {
    window.gameCore = gameCore;
}

// Export for debugging
window.game = gameCore.debug;
