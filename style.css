@import url('https://fonts.googleapis.com/css2?family=IM+Fell+English+SC&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'IM Fell English SC', serif;
    background: linear-gradient(45deg, #2d1b3d, #3d2f1f);
    color: #d4af37;
    overflow: hidden;
    user-select: none;
}

/* ===== TITLE SCREEN STYLES ===== */
.title-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    display: none; /* Hidden by default */
    align-items: center;
    justify-content: center;
    background: linear-gradient(45deg, #1a0f1f, #2d1b3d, #1f1a12);
    opacity: 1;
    transition: opacity 0.8s ease-in-out;
}

.title-screen[style*="display: flex"] {
    display: flex !important;
}

.title-screen.fade-out {
    opacity: 0;
}

.title-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0.8;
    z-index: 1;
}

.title-content {
    position: relative;
    z-index: 2;
    text-align: center;
    padding: 40px;
    background: rgba(0, 0, 0, 0.7);
    border: 3px solid #b8860b;
    border-radius: 15px;
    box-shadow: 0 0 30px rgba(184, 134, 11, 0.5);
    backdrop-filter: blur(5px);
}

.title-text {
    font-size: 4rem;
    font-weight: bold;
    color: #d4af37;
    text-shadow:
        0 0 10px #d4af37,
        0 0 20px #b8860b,
        0 0 30px #8b6914,
        2px 2px 4px rgba(0, 0, 0, 0.8);
    margin-bottom: 20px;
    letter-spacing: 3px;
    animation: title-glow 3s ease-in-out infinite alternate;
    animation-play-state: running;
}

.title-subtitle {
    font-size: 1.2rem;
    color: #cccccc;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
    margin-bottom: 40px;
    font-style: italic;
    opacity: 0.9;
}

/* PUSH ANY KEY button (Stage 1) */
.title-push-key-button {
    font-family: 'IM Fell English SC', serif;
    font-size: 1.3rem;
    font-weight: bold;
    padding: 12px 35px;
    background: linear-gradient(145deg, #2a1f15, #3d2f1f);
    border: 2px solid #8b6914;
    color: #b8860b;
    cursor: pointer;
    border-radius: 6px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1.5px;
    box-shadow:
        0 3px 6px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.08);
    animation: push-key-pulse 2s ease-in-out infinite;
}

.title-push-key-button:hover {
    background: linear-gradient(145deg, #3d2f1f, #4a3625);
    box-shadow:
        0 0 15px #b8860b,
        0 4px 8px rgba(0, 0, 0, 0.6),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
    color: #d4af37;
}

.title-push-key-button:active {
    transform: translateY(0);
    box-shadow:
        0 0 10px #b8860b,
        0 1px 3px rgba(0, 0, 0, 0.6),
        inset 0 1px 0 rgba(255, 255, 255, 0.08);
}

@keyframes push-key-pulse {
    0%, 100% {
        opacity: 0.8;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.02);
    }
}

/* Button container for GAME START and OPTION buttons */
.title-button-container {
    display: flex;
    gap: 20px;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}

/* GAME START button (Stage 2) */
.title-start-button {
    font-family: 'IM Fell English SC', serif;
    font-size: 1.5rem;
    font-weight: bold;
    padding: 15px 40px;
    background: linear-gradient(145deg, #3d2f1f, #5c4a35);
    border: 3px solid #b8860b;
    color: #d4af37;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 2px;
    box-shadow:
        0 4px 8px rgba(0, 0, 0, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.title-start-button:hover {
    background: linear-gradient(145deg, #5c4a35, #8b6b47);
    box-shadow:
        0 0 20px #d4af37,
        0 6px 12px rgba(0, 0, 0, 0.7),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    color: #ffffff;
}

.title-start-button:active {
    transform: translateY(0);
    box-shadow:
        0 0 15px #d4af37,
        0 2px 4px rgba(0, 0, 0, 0.7),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* OPTION button (Stage 2) */
.title-option-button {
    font-family: 'IM Fell English SC', serif;
    font-size: 1.3rem;
    font-weight: bold;
    padding: 12px 30px;
    background: linear-gradient(145deg, #2a1f15, #3d2f1f);
    border: 2px solid #8b6914;
    color: #b8860b;
    cursor: pointer;
    border-radius: 6px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1.5px;
    box-shadow:
        0 3px 6px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.08);
}

.title-option-button:hover {
    background: linear-gradient(145deg, #3d2f1f, #5c4a35);
    border-color: #b8860b;
    color: #d4af37;
    box-shadow:
        0 0 15px #b8860b,
        0 4px 8px rgba(0, 0, 0, 0.6),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
}

.title-option-button:active {
    transform: translateY(0);
    box-shadow:
        0 0 10px #b8860b,
        0 2px 4px rgba(0, 0, 0, 0.6),
        inset 0 1px 0 rgba(255, 255, 255, 0.08);
}

@keyframes title-glow {
    0% {
        text-shadow:
            0 0 10px #d4af37,
            0 0 20px #b8860b,
            0 0 30px #8b6914,
            2px 2px 4px rgba(0, 0, 0, 0.8);
    }
    100% {
        text-shadow:
            0 0 15px #d4af37,
            0 0 25px #b8860b,
            0 0 35px #8b6914,
            2px 2px 4px rgba(0, 0, 0, 0.8);
    }
}

/* ===== OPTION SCREEN STYLES ===== */
.option-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9998;
    display: none;
    align-items: center;
    justify-content: center;
    background: linear-gradient(45deg, #1a0f1f, #2d1b3d, #1f1a12);
    opacity: 1;
    transition: opacity 0.8s ease-in-out;
    font-family: 'IM Fell English SC', serif;
}

.option-screen[style*="display: flex"] {
    display: flex !important;
}

.option-screen.fade-out {
    opacity: 0;
}

.option-content {
    text-align: center;
    max-width: 600px;
    padding: 40px;
    background: rgba(0, 0, 0, 0.3);
    border: 2px solid #8b6914;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.8);
}

.option-title {
    font-size: 2.5rem;
    color: #d4af37;
    text-shadow:
        0 0 10px #d4af37,
        0 0 20px #b8860b,
        2px 2px 4px rgba(0, 0, 0, 0.8);
    margin-bottom: 30px;
    text-transform: uppercase;
    letter-spacing: 3px;
}

.option-subtitle {
    font-size: 1.2rem;
    color: #cccccc;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
    margin-bottom: 40px;
    font-style: italic;
    opacity: 0.9;
}

.option-section {
    margin-bottom: 30px;
    padding: 20px;
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid #5c4a35;
    border-radius: 8px;
}

.option-section-title {
    font-size: 1.4rem;
    color: #b8860b;
    margin-bottom: 15px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.option-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 20px;
}

.option-button {
    font-family: 'IM Fell English SC', serif;
    font-size: 1.1rem;
    font-weight: bold;
    padding: 10px 25px;
    background: linear-gradient(145deg, #2a1f15, #3d2f1f);
    border: 2px solid #8b6914;
    color: #b8860b;
    cursor: pointer;
    border-radius: 6px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow:
        0 3px 6px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.08);
}

.option-button:hover {
    background: linear-gradient(145deg, #3d2f1f, #5c4a35);
    border-color: #b8860b;
    color: #d4af37;
    box-shadow:
        0 0 15px #b8860b,
        0 4px 8px rgba(0, 0, 0, 0.6),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
}

.option-button:active {
    transform: translateY(0);
    box-shadow:
        0 0 10px #b8860b,
        0 2px 4px rgba(0, 0, 0, 0.6),
        inset 0 1px 0 rgba(255, 255, 255, 0.08);
}

.option-button.active {
    background: linear-gradient(145deg, #5c4a35, #8b6b47);
    border-color: #d4af37;
    color: #ffffff;
    box-shadow:
        0 0 20px #d4af37,
        0 4px 8px rgba(0, 0, 0, 0.7),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.option-back-button {
    font-family: 'IM Fell English SC', serif;
    font-size: 1.3rem;
    font-weight: bold;
    padding: 12px 30px;
    background: linear-gradient(145deg, #3d2f1f, #5c4a35);
    border: 2px solid #b8860b;
    color: #d4af37;
    cursor: pointer;
    border-radius: 6px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1.5px;
    box-shadow:
        0 4px 8px rgba(0, 0, 0, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.option-back-button:hover {
    background: linear-gradient(145deg, #5c4a35, #8b6b47);
    box-shadow:
        0 0 20px #d4af37,
        0 6px 12px rgba(0, 0, 0, 0.7),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    color: #ffffff;
}

.option-back-button:active {
    transform: translateY(0);
    box-shadow:
        0 0 15px #d4af37,
        0 2px 4px rgba(0, 0, 0, 0.7),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.game-container {
    display: none; /* Initially hidden for fade-in effect */
    height: 100vh;
    background: #1f1a12;
    border: 3px solid #b8860b;
    opacity: 1;
    transition: opacity 0.8s ease-in-out;
}

/* Fullscreen mode adjustments */
:fullscreen .game-container,
:-webkit-full-screen .game-container,
:-moz-full-screen .game-container,
:-ms-fullscreen .game-container {
    width: 100vw !important;
    height: 100vh !important;
    max-width: none !important;
    max-height: none !important;
    border: none;
    margin: 0;
    padding: 0;
}

:fullscreen #gameCanvas,
:-webkit-full-screen #gameCanvas,
:-moz-full-screen #gameCanvas,
:-ms-fullscreen #gameCanvas {
    max-width: none !important;
    max-height: none !important;
    object-fit: contain;
}

/* Ensure UI elements scale properly in fullscreen */
:fullscreen .main-view,
:-webkit-full-screen .main-view,
:-moz-full-screen .main-view,
:-ms-fullscreen .main-view {
    width: 100% !important;
    height: 100% !important;
}

:fullscreen .ui-panel,
:-webkit-full-screen .ui-panel,
:-moz-full-screen .ui-panel,
:-ms-fullscreen .ui-panel {
    height: 100% !important;
}

/* Game container fade-in animation */
.game-container.fade-in {
    display: flex !important; /* Show when fade-in starts */
    opacity: 0;
}

.game-container.fade-in.show {
    opacity: 1;
}

.main-view {
    flex: 2;
    position: relative;
    background: #000;
}

#gameCanvas {
    width: 100%;
    height: 100%;
    background: 
        /* Floor texture layers */
        radial-gradient(ellipse 400px 100px at 50% 85%, rgba(40, 40, 40, 0.6) 0%, transparent 100%),
        radial-gradient(ellipse 300px 80px at 30% 90%, rgba(60, 60, 60, 0.4) 0%, transparent 100%),
        radial-gradient(ellipse 350px 90px at 70% 88%, rgba(50, 50, 50, 0.5) 0%, transparent 100%),
        /* Stone tile pattern */
        repeating-linear-gradient(
            45deg,
            rgba(45, 45, 45, 0.3) 0px,
            rgba(35, 35, 35, 0.3) 20px,
            rgba(40, 40, 40, 0.3) 40px
        ),
        repeating-linear-gradient(
            -45deg,
            rgba(50, 50, 50, 0.2) 0px,
            rgba(30, 30, 30, 0.2) 15px,
            rgba(45, 45, 45, 0.2) 30px
        ),
        /* Atmospheric lighting */
        radial-gradient(ellipse at top, rgba(0, 17, 34, 0.8) 0%, transparent 50%),
        radial-gradient(ellipse at bottom, rgba(0, 0, 0, 0.9) 0%, transparent 50%),
        linear-gradient(180deg, #001122 0%, #000811 25%, #000000 50%, #000411 75%, #000000 100%);
    image-rendering: pixelated;
    cursor: crosshair;
    position: relative;
}

#gameCanvas::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        /* Floor reflections and highlights */
        radial-gradient(ellipse 200px 20px at 50% 80%, rgba(255, 255, 255, 0.05) 0%, transparent 100%),
        radial-gradient(ellipse 150px 15px at 30% 85%, rgba(255, 255, 255, 0.03) 0%, transparent 100%),
        radial-gradient(ellipse 180px 18px at 70% 83%, rgba(255, 255, 255, 0.04) 0%, transparent 100%),
        /* Scan lines */
        repeating-linear-gradient(
            90deg,
            transparent 0px,
            rgba(0, 255, 65, 0.02) 1px,
            transparent 2px
        ),
        repeating-linear-gradient(
            0deg,
            transparent 0px,
            rgba(0, 255, 65, 0.01) 1px,
            transparent 4px
        );
    pointer-events: none;
    z-index: 1;
}

#gameCanvas::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        /* Dust particles and atmospheric effects */
        radial-gradient(circle 2px at 15% 75%, rgba(200, 200, 200, 0.1) 0%, transparent 50%),
        radial-gradient(circle 1px at 25% 80%, rgba(180, 180, 180, 0.08) 0%, transparent 50%),
        radial-gradient(circle 1.5px at 45% 78%, rgba(220, 220, 220, 0.06) 0%, transparent 50%),
        radial-gradient(circle 1px at 65% 82%, rgba(190, 190, 190, 0.07) 0%, transparent 50%),
        radial-gradient(circle 2px at 85% 76%, rgba(210, 210, 210, 0.09) 0%, transparent 50%),
        /* Floor moisture effects */
        radial-gradient(ellipse 40px 8px at 20% 88%, rgba(100, 150, 200, 0.05) 0%, transparent 100%),
        radial-gradient(ellipse 60px 12px at 80% 90%, rgba(80, 120, 180, 0.03) 0%, transparent 100%);
    pointer-events: none;
    z-index: 2;
    animation: floor-shimmer 8s ease-in-out infinite alternate;
}

@keyframes floor-shimmer {
    0% {
        opacity: 0.6;
        transform: translateY(0px);
    }
    100% {
        opacity: 1;
        transform: translateY(-1px);
    }
}

.ui-panel {
    flex: 1;
    background: linear-gradient(180deg, #1f1a12, #2d1f17);
    border-left: 3px solid #b8860b;
    padding: 15px;
    overflow-y: auto;
}

.party-info {
    background: rgba(212, 175, 55, 0.1);
    border: 2px solid #b8860b;
    padding: 10px;
    margin-bottom: 15px;
    border-radius: 5px;
}

.character {
    margin-bottom: 10px;
    padding: 8px;
    background: rgba(139, 69, 19, 0.2);
    border: 1px solid #8b4513;
    border-radius: 3px;
}

.character.dead {
    background: rgba(100, 0, 0, 0.3);
    border-color: #660000;
    color: #ff4444;
}

.char-name {
    font-weight: bold;
    font-size: 14px;
    color: #d4af37;
}

.char-stats {
    font-size: 11px;
    margin-top: 3px;
}

.health-bar {
    width: 100%;
    height: 12px;
    background: #2d1a0e;
    border: 1px solid #8b4513;
    margin: 3px 0;
    position: relative;
}

.health-fill {
    height: 100%;
    background: linear-gradient(90deg, #8b0000, #cd853f, #228b22);
    transition: width 0.3s ease;
}

.game-info {
    background: rgba(212, 175, 55, 0.1);
    border: 2px solid #b8860b;
    padding: 10px;
    margin-bottom: 15px;
    border-radius: 5px;
}

.controls {
    background: rgba(212, 175, 55, 0.1);
    border: 2px solid #b8860b;
    padding: 10px;
    margin-bottom: 15px;
    border-radius: 5px;
}

.control-btn {
    display: block;
    width: 100%;
    padding: 8px;
    margin: 5px 0;
    background: linear-gradient(145deg, #3d2f1f, #5c4a35);
    border: 2px solid #b8860b;
    color: #d4af37;
    font-family: 'IM Fell English SC', serif;
    font-size: 12px;
    cursor: pointer;
    border-radius: 3px;
    transition: all 0.2s;
}

.control-btn:hover {
    background: linear-gradient(145deg, #5c4a35, #8b6b47);
    box-shadow: 0 0 10px #d4af37;
}

.control-btn:disabled {
    background: #333;
    color: #666;
    cursor: not-allowed;
    box-shadow: none;
}

.combat-ui {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('assets/images/combat.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-color: rgba(0, 0, 0, 0.9);
    z-index: 100;
}

.combat-ui.active {
    display: block;
}

.combat-layout {
    display: grid;
    grid-template-columns: 1.5fr 2fr 1.5fr;
    height: 100vh;
    gap: 20px;
    padding: 20px;
}

.enemy-area-left,
.enemy-area-right {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 750px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
}

.combat-center {
    display: flex;
    flex-direction: column;
    justify-content: center;
    background: linear-gradient(145deg, #1f1a12, #2d1f17);
    border: 3px solid #b8860b;
    padding: 20px;
    border-radius: 10px;
    max-height: 650px;
    overflow-y: auto;
}

.enemy-card {
    padding: 20px;
    background: rgba(255, 0, 0, 0.1);
    border: 2px solid #ff4444;
    border-radius: 8px;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 280px;
    max-width: 400px;
    margin: 0 auto;
    min-height: 650px;
}

.enemy-card.defeated {
    background: rgba(100, 0, 0, 0.2);
    border-color: #660000;
    opacity: 0.6;
}

/* Enemy image styling */
.enemy-image-container {
    width: 280px;
    height: 420px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.3);
    border: 2px solid #ff4444;
    border-radius: 8px;
    overflow: hidden;
}

.enemy-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    image-rendering: pixelated;
}

.enemy-image-fallback {
    font-size: 64px;
    color: #ff6666;
    text-shadow: 0 0 10px #ff6666;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    text-align: center;
}

/* Enemy info section */
.enemy-info {
    width: 100%;
}

.enemy-name {
    font-size: 18px;
    font-weight: bold;
    color: #ff6666;
    margin-bottom: 10px;
    text-shadow: 0 0 5px #ff6666;
}

.enemy-card.defeated .enemy-name {
    color: #aa4444;
}

.enemy-stats {
    font-size: 12px;
    color: #cccccc;
    margin-top: 6px;
    line-height: 1.4;
}

.enemy-status {
    font-size: 14px;
    font-weight: bold;
    margin-top: 10px;
    padding: 6px 12px;
    border-radius: 4px;
    background: rgba(255, 100, 100, 0.2);
}

.enemy-status.alive {
    color: #ff6666;
    background: rgba(255, 100, 100, 0.2);
}

.enemy-status.defeated {
    color: #aa4444;
    background: rgba(170, 68, 68, 0.2);
}

.combat-party-info {
    margin: 15px 0;
    padding: 12px;
    background: rgba(212, 175, 55, 0.1);
    border: 2px solid #b8860b;
    border-radius: 5px;
}

.combat-party-info h4 {
    margin: 0 0 10px 0;
    text-align: center;
    font-size: 14px;
    color: #d4af37;
}

.combat-party-members {
    display: flex;
    flex-direction: row;
    gap: 8px;
    justify-content: space-between;
}

.combat-party-card {
    padding: 8px;
    background: rgba(139, 69, 19, 0.2);
    border: 1px solid #8b4513;
    border-radius: 3px;
    text-align: center;
    transition: all 0.3s ease;
    flex: 1;
    min-width: 120px;
}

.combat-party-card.current-turn {
    background: rgba(212, 175, 55, 0.3);
    border: 2px solid #d4af37;
    box-shadow: 0 0 8px rgba(212, 175, 55, 0.5);
    animation: party-turn-pulse 1.5s ease-in-out infinite alternate;
}

.combat-party-card.dead {
    background: rgba(100, 0, 0, 0.3);
    border-color: #660000;
    opacity: 0.6;
}

.combat-party-name {
    font-weight: bold;
    font-size: 11px;
    color: #d4af37;
    margin-bottom: 3px;
}

.combat-party-card.dead .combat-party-name {
    color: #ff4444;
}

.combat-party-level {
    font-size: 9px;
    color: #cccccc;
    margin-bottom: 3px;
}

.combat-party-health {
    width: 100%;
    height: 8px;
    background: #2d1a0e;
    border: 1px solid #8b4513;
    margin: 2px 0;
    position: relative;
}

.combat-party-health-fill {
    height: 100%;
    background: linear-gradient(90deg, #8b0000, #cd853f, #228b22);
    transition: width 0.3s ease;
}

.combat-party-stats {
    font-size: 8px;
    color: #cccccc;
    margin-top: 2px;
    line-height: 1.1;
}

.combat-party-status {
    font-size: 9px;
    font-weight: bold;
    margin-top: 2px;
}

.combat-party-status.defending {
    color: #4169E1;
}

.combat-party-status.dead {
    color: #ff4444;
}

@keyframes party-turn-pulse {
    0% { 
        box-shadow: 0 0 8px rgba(212, 175, 55, 0.5);
        transform: scale(1);
    }
    100% { 
        box-shadow: 0 0 12px rgba(212, 175, 55, 0.8);
        transform: scale(1.02);
    }
}

.current-turn-info {
    text-align: center;
    margin: 15px 0;
    padding: 12px;
    background: rgba(212, 175, 55, 0.1);
    border: 2px solid #b8860b;
    border-radius: 5px;
    animation: turn-glow 2s ease-in-out infinite alternate;
}

.turn-indicator {
    margin-bottom: 8px;
}

.turn-label {
    font-size: 14px;
    color: #d4af37;
    margin-right: 10px;
}

.turn-member {
    font-size: 16px;
    font-weight: bold;
    color: #d4af37;
    text-shadow: 0 0 5px #d4af37;
}

.turn-member.enemy-turn {
    color: #ff6666;
    text-shadow: 0 0 5px #ff6666;
}

.turn-member-stats {
    font-size: 11px;
    color: #cccccc;
    margin-top: 5px;
}

@keyframes turn-glow {
    0% { 
        box-shadow: 0 0 5px rgba(212, 175, 55, 0.3);
        border-color: #b8860b;
    }
    100% { 
        box-shadow: 0 0 15px rgba(212, 175, 55, 0.6);
        border-color: #d4af37;
    }
}

@keyframes enemy-turn-glow {
    0% { 
        box-shadow: 0 0 5px rgba(255, 100, 100, 0.3);
        border-color: #ff6666;
    }
    100% { 
        box-shadow: 0 0 15px rgba(255, 100, 100, 0.6);
        border-color: #ff4444;
    }
}

.combat-actions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr 1fr;
    gap: 10px;
    margin: 20px 0;
}

.action-btn {
    padding: 12px;
    background: linear-gradient(145deg, #3d2f1f, #5c4a35);
    border: 2px solid #b8860b;
    color: #d4af37;
    font-family: 'IM Fell English SC', serif;
    font-size: 14px;
    cursor: pointer;
    border-radius: 5px;
    transition: all 0.2s;
}

.action-btn:hover {
    background: linear-gradient(145deg, #5c4a35, #8b6b47);
    box-shadow: 0 0 15px #d4af37;
}

.combat-log {
    height: 200px;
    overflow-y: auto;
    background: rgba(0, 0, 0, 0.5);
    border: 1px solid #666;
    padding: 10px;
    font-size: 12px;
    margin-top: 10px;
}

.log-entry {
    margin: 2px 0;
    padding: 2px;
}

.log-damage {
    color: #ff6666;
}

.log-heal {
    color: #66ff66;
}

.log-system {
    color: #ffff66;
}

.floor-info {
    position: absolute;
    top: 10px;
    left: 10px;
    background: rgba(0, 0, 0, 0.8);
    padding: 10px;
    border: 2px solid #00ff41;
    border-radius: 5px;
    color: #00ff41;
    font-size: 14px;
}

.level-up-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    z-index: 200;
    justify-content: center;
    align-items: center;
}

.level-up-modal.active {
    display: flex;
}

.level-up-content {
    background: linear-gradient(145deg, #001122, #002244);
    border: 3px solid #00ffaa;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    max-width: 400px;
}

.level-up-title {
    font-size: 24px;
    color: #00ffaa;
    margin-bottom: 20px;
    text-shadow: 0 0 10px #00ffaa;
}

.stat-increases {
    margin: 20px 0;
    font-size: 14px;
}

.stat-increase {
    margin: 5px 0;
    color: #66ff66;
}

.level-up-member {
    background: rgba(0, 255, 170, 0.1);
    border: 2px solid #00ffaa;
    margin: 15px 0;
    padding: 15px;
    border-radius: 5px;
}

.level-up-member-name {
    font-size: 18px;
    color: #00ffaa;
    margin-bottom: 10px;
    text-shadow: 0 0 5px #00ffaa;
}

.level-up-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 5px;
    font-size: 12px;
}

@keyframes flicker {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.game-title {
    text-align: center;
    margin-bottom: 15px;
    font-size: 16px;
    color: #00ffaa;
    text-shadow: 0 0 10px #00ffaa;
    animation: flicker 2s infinite;
    animation-play-state: running;
}

.minimap {
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid #00ff41;
    padding: 10px;
    margin-top: 10px;
    border-radius: 5px;
    position: absolute;
    bottom: 20px;
    right: 20px;
    width: 220px;
    height: 220px;
}

.minimap h4 {
    margin-bottom: 5px;
    text-align: center;
    font-size: 12px;
}

.minimap-grid {
    display: grid;
    grid-template-columns: repeat(20, 1fr);
    gap: 1px;
    width: 200px;
    height: 200px;
}

.minimap-cell {
    width: 9px;
    height: 9px;
    background: #333;
    border: none;
}

.minimap-cell.wall {
    background: 
        radial-gradient(circle at 30% 30%, #888 0%, #555 40%, #333 100%),
        linear-gradient(45deg, #777 25%, transparent 25%, transparent 75%, #777 75%, #777),
        linear-gradient(-45deg, #666 25%, transparent 25%, transparent 75%, #666 75%, #666);
    background-size: 9px 9px, 3px 3px, 3px 3px;
    box-shadow: 
        inset 1px 1px 0 rgba(150, 150, 150, 0.3),
        inset -1px -1px 0 rgba(0, 0, 0, 0.5);
    border: 1px solid #444;
}

.minimap-cell.floor {
    background: 
        radial-gradient(circle at 40% 40%, #333 0%, #222 50%, #111 100%),
        repeating-linear-gradient(45deg, #2a2a2a 0px, #1a1a1a 2px, #242424 4px);
    background-size: 9px 9px, 3px 3px;
    box-shadow: 
        inset 1px 1px 0 rgba(100, 100, 100, 0.2),
        inset -1px -1px 0 rgba(0, 0, 0, 0.6);
    border: 1px solid #1a1a1a;
}

.minimap-cell.visited {
    background: 
        radial-gradient(circle at 40% 40%, #226622 0%, #004400 50%, #002200 100%),
        repeating-linear-gradient(45deg, #115511 0px, #003300 2px, #004400 4px),
        linear-gradient(135deg, #006600 25%, transparent 25%, transparent 75%, #006600 75%);
    background-size: 9px 9px, 3px 3px, 2px 2px;
    box-shadow: 
        inset 1px 1px 0 rgba(150, 255, 150, 0.15),
        inset -1px -1px 0 rgba(0, 0, 0, 0.7),
        0 0 1px rgba(0, 255, 0, 0.3);
    border: 1px solid #002200;
}

.minimap-cell.player {
    background: #1900ff;
    box-shadow: 0 0 3px #1900ff;
    border-radius: 50%;
}

.minimap-cell.enemy {
    background: #ff4444;
}

.minimap-cell.stairs {
    background: #ffff44;
}

.minimap-cell.door {
    background: #8B4513;
}

.minimap-cell.heal {
    background: #00ffff;
}

.minimap-cell.boss {
    background: #ff00ff;
}

.game-over {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.95);
    z-index: 300;
    justify-content: center;
    align-items: center;
}

.game-over.active {
    display: flex;
}

.game-over-content {
    text-align: center;
    color: #ff4444;
    font-size: 24px;
}

.victory-screen {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.95);
    z-index: 300;
    justify-content: center;
    align-items: center;
}

.victory-screen.active {
    display: flex;
}

.victory-content {
    text-align: center;
    color: #00ffaa;
    font-size: 24px;
}

/* Mapping Tool Notification Modal */
.mapping-tool-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    z-index: 250;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}

.mapping-tool-modal.active {
    display: flex;
}

.mapping-tool-content {
    background: linear-gradient(145deg, #001122, #002244);
    border: 3px solid #d4af37;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    max-width: 500px;
    box-shadow: 0 0 20px rgba(212, 175, 55, 0.5);
    cursor: pointer;
    position: relative;
}

.mapping-tool-title {
    font-size: 24px;
    color: #d4af37;
    margin-bottom: 20px;
    text-shadow: 0 0 10px #d4af37;
}

/* Health Potion Notification Modal */
.health-potion-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    z-index: 260;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
}

.health-potion-modal.active {
    display: flex;
}

.health-potion-content {
    background: linear-gradient(145deg, #1a0f0f, #2d1a1a);
    border: 3px solid #ff6b6b;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    max-width: 500px;
    box-shadow: 0 0 20px rgba(255, 107, 107, 0.5);
    cursor: pointer;
    position: relative;
    transform: scale(0.9);
    transition: transform 0.3s ease-in-out;
}

.health-potion-modal.active .health-potion-content {
    transform: scale(1);
}

.health-potion-title {
    font-size: 24px;
    color: #ff6b6b;
    margin-bottom: 20px;
    text-shadow: 0 0 10px #ff6b6b;
    font-weight: bold;
}

.health-potion-image {
    max-width: 90vw;
    max-height: 70vh;
    margin: 20px auto;
    display: block;
    border-radius: 10px;
    box-shadow: 0 0 15px rgba(255, 107, 107, 0.3);
    filter: drop-shadow(0 0 10px rgba(255, 107, 107, 0.5));
    object-fit: contain;
}

.health-potion-description {
    font-size: 16px;
    color: #cccccc;
    margin: 20px 0;
    line-height: 1.5;
}

.health-potion-dismiss {
    font-size: 12px;
    color: #999999;
    margin-top: 20px;
    font-style: italic;
}

/* Smoke Screen Notification Modal */
.smoke-screen-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    z-index: 260;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
}

.smoke-screen-modal.active {
    display: flex;
}

.smoke-screen-content {
    background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
    border: 3px solid #888888;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    max-width: 500px;
    box-shadow: 0 0 20px rgba(136, 136, 136, 0.5);
    cursor: pointer;
    position: relative;
    transform: scale(0.9);
    transition: transform 0.3s ease-in-out;
}

.smoke-screen-modal.active .smoke-screen-content {
    transform: scale(1);
}

.smoke-screen-title {
    font-size: 24px;
    color: #888888;
    margin-bottom: 20px;
    text-shadow: 0 0 10px #888888;
    font-weight: bold;
}

.smoke-screen-image {
    max-width: 90vw;
    max-height: 70vh;
    margin: 20px auto;
    display: block;
    border-radius: 10px;
    box-shadow: 0 0 15px rgba(136, 136, 136, 0.3);
    filter: drop-shadow(0 0 10px rgba(136, 136, 136, 0.5));
    object-fit: contain;
}

.smoke-screen-description {
    font-size: 16px;
    color: #cccccc;
    margin: 20px 0;
    line-height: 1.5;
}

.smoke-screen-dismiss {
    font-size: 12px;
    color: #999999;
    margin-top: 20px;
    font-style: italic;
}

/* Antidote Notification Modal */
.antidote-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
}

.antidote-modal.active {
    display: flex;
}

.antidote-content {
    background: linear-gradient(145deg, #0f1a0f, #1a2d1a);
    border: 3px solid #4CAF50;
    padding: 30px;
    border-radius: 15px;
    max-width: 400px;
    width: 90%;
    text-align: center;
    box-shadow: 0 0 30px rgba(76, 175, 80, 0.6);
    position: relative;
    transform: scale(0.9);
    transition: transform 0.3s ease-in-out;
}

.antidote-modal.active .antidote-content {
    transform: scale(1);
}

.antidote-title {
    font-size: 24px;
    color: #4CAF50;
    margin-bottom: 20px;
    text-shadow: 0 0 10px #4CAF50;
    font-weight: bold;
}

.antidote-image {
    max-width: 90vw;
    max-height: 70vh;
    object-fit: contain;
    margin: 20px auto;
    display: block;
    filter: drop-shadow(0 0 10px rgba(76, 175, 80, 0.5));
}

.antidote-description {
    color: #e0e0e0;
    font-size: 16px;
    line-height: 1.5;
    margin: 20px 0;
}

.antidote-dismiss {
    color: #888;
    font-size: 14px;
    margin-top: 20px;
    font-style: italic;
}

/* Healing Point Notification Modal */
.healing-point-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
}

.healing-point-modal.active {
    display: flex;
}

.healing-point-content {
    background: linear-gradient(145deg, #0f1a1a, #1a2d2d);
    border: 3px solid #00ffff;
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    max-width: 400px;
    width: 90%;
    box-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
    position: relative;
    transform: scale(0.9);
    transition: transform 0.3s ease-in-out;
}

.healing-point-modal.active .healing-point-content {
    transform: scale(1);
}

.healing-point-title {
    font-size: 24px;
    font-weight: bold;
    color: #00ffff;
    margin-bottom: 20px;
    text-shadow: 0 0 10px #00ffff;
}

.healing-point-image {
    max-width: 90vw;
    max-height: 70vh;
    margin: 20px auto;
    display: block;
    border: 2px solid #00ffff;
    border-radius: 5px;
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
    object-fit: contain;
}

.healing-point-description {
    font-size: 16px;
    color: #cccccc;
    line-height: 1.5;
    margin: 20px 0;
}

.healing-point-dismiss {
    color: #888;
    font-size: 14px;
    margin-top: 20px;
    font-style: italic;
}

/* Floor Progression Notification Modal */
.floor-progression-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    z-index: 1000;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
}

.floor-progression-modal.active {
    display: flex;
}

.floor-progression-content {
    background: linear-gradient(145deg, #1a1a0f, #2d2d1a);
    border: 3px solid #d4af37;
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    max-width: 90vw;
    max-height: 90vh;
    box-shadow: 0 0 30px rgba(212, 175, 55, 0.3);
    position: relative;
    transform: scale(0.9);
    transition: transform 0.3s ease-in-out;
    overflow: auto;
}

.floor-progression-modal.active .floor-progression-content {
    transform: scale(1);
}

.floor-progression-title {
    font-size: 24px;
    font-weight: bold;
    color: #d4af37;
    margin-bottom: 20px;
    text-shadow: 0 0 10px #d4af37;
}

.floor-progression-image {
    max-width: 100%;
    height: auto;
    margin: 20px auto;
    display: block;
    border: 2px solid #d4af37;
    border-radius: 10px;
    box-shadow: 0 0 15px rgba(212, 175, 55, 0.3);
    filter: drop-shadow(0 0 10px rgba(212, 175, 55, 0.5));
    object-fit: contain;
}

.floor-progression-description {
    font-size: 16px;
    color: #cccccc;
    line-height: 1.5;
    margin: 20px 0;
}

.floor-progression-dismiss {
    color: #888;
    font-size: 14px;
    margin-top: 20px;
    font-style: italic;
}

.mapping-tool-image {
    max-width: 90vw;
    max-height: 70vh;
    margin: 20px auto;
    display: block;
    border: 2px solid #d4af37;
    border-radius: 5px;
    box-shadow: 0 0 15px rgba(212, 175, 55, 0.3);
    object-fit: contain;
}

.mapping-tool-description {
    font-size: 16px;
    color: #cccccc;
    margin: 20px 0;
    line-height: 1.4;
}

.mapping-tool-dismiss {
    font-size: 12px;
    color: #999999;
    margin-top: 15px;
    font-style: italic;
}

.door-ui {
    display: none;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.9);
    border: 2px solid #00ff41;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    z-index: 150;
}

.door-ui.active {
    display: block;
}

/* Enhanced Wall Texture Classes for Different Dungeon Depths */
.wall-texture-shallow {
    background: 
        radial-gradient(circle at 20% 20%, #999 0%, #666 30%, #444 70%, #222 100%),
        linear-gradient(45deg, #888 25%, transparent 25%, transparent 75%, #888 75%),
        linear-gradient(-45deg, #777 25%, transparent 25%, transparent 75%, #777 75%);
    background-size: 8px 8px, 4px 4px, 4px 4px;
    box-shadow: 
        inset 2px 2px 4px rgba(200, 200, 200, 0.2),
        inset -2px -2px 4px rgba(0, 0, 0, 0.8);
}

.wall-texture-deep {
    background: 
        radial-gradient(ellipse at 30% 30%, #555 0%, #333 40%, #111 100%),
        repeating-linear-gradient(90deg, #444 0px, #333 2px, #222 4px),
        repeating-linear-gradient(0deg, #333 0px, #222 1px, #111 2px);
    background-size: 12px 12px, 6px 6px, 6px 6px;
    box-shadow: 
        inset 1px 1px 2px rgba(100, 100, 100, 0.15),
        inset -2px -2px 6px rgba(0, 0, 0, 0.9);
}

.wall-texture-abyssal {
    background: 
        radial-gradient(circle at 40% 40%, #333 0%, #111 50%, #000 100%),
        repeating-conic-gradient(from 0deg at 50% 50%, #222 0deg, #000 90deg, #111 180deg, #000 270deg),
        linear-gradient(135deg, #222 25%, #000 25%, #000 75%, #111 75%);
    background-size: 10px 10px, 8px 8px, 6px 6px;
    box-shadow: 
        inset 1px 1px 1px rgba(80, 80, 80, 0.1),
        inset -3px -3px 8px rgba(0, 0, 0, 1);
}

/* Enhanced Floor and Ceiling Textures */
.floor-texture-stone {
    background: 
        radial-gradient(circle at 60% 40%, #444 0%, #222 60%, #111 100%),
        repeating-linear-gradient(45deg, #333 0px, #222 3px, #111 6px);
    background-size: 15px 15px, 8px 8px;
}

.ceiling-texture-ancient {
    background: 
        linear-gradient(180deg, #001a33 0%, #000811 50%, #000000 100%),
        repeating-radial-gradient(circle at 50% 50%, #002244 0px, transparent 20px);
    background-size: auto, 40px 40px;
}

/* Atmospheric Lighting Effects */
.dungeon-atmosphere::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(ellipse 800px 400px at 50% 100%, rgba(0, 255, 65, 0.03) 0%, transparent 70%),
        radial-gradient(ellipse 600px 300px at 50% 0%, rgba(0, 170, 255, 0.02) 0%, transparent 60%);
    pointer-events: none;
    z-index: 2;
}

/* Enhanced Door Texture */
.minimap-cell.door {
    background: 
        linear-gradient(45deg, #8B4513 25%, #A0522D 25%, #A0522D 50%, #8B4513 50%, #8B4513 75%, #A0522D 75%),
        radial-gradient(circle at 50% 50%, #CD853F 0%, #8B4513 70%);
    background-size: 4px 4px, 9px 9px;
    box-shadow: 
        inset 1px 1px 0 rgba(205, 133, 63, 0.4),
        inset -1px -1px 0 rgba(0, 0, 0, 0.6);
    border: 1px solid #654321;
}

/* Enhanced Stair Texture */
.minimap-cell.stairs {
    background: 
        linear-gradient(135deg, #ffff44 25%, #ffcc00 25%, #ffcc00 50%, #ffff44 50%, #ffff44 75%, #ffcc00 75%),
        radial-gradient(circle at 30% 30%, #ffffff 0%, #ffff44 40%, #cc9900 100%);
    background-size: 3px 3px, 9px 9px;
    box-shadow: 
        inset 1px 1px 0 rgba(255, 255, 255, 0.5),
        inset -1px -1px 0 rgba(204, 153, 0, 0.7);
}

/* Enhanced Heal Point Texture */
.minimap-cell.heal {
    background: 
        radial-gradient(circle at 50% 50%, #00ffff 0%, #00cccc 30%, #008888 70%, #004444 100%),
        repeating-conic-gradient(from 0deg at 50% 50%, #00ffff 0deg, #00cccc 45deg, #00aaaa 90deg, #00cccc 135deg);
    background-size: 9px 9px, 6px 6px;
    box-shadow: 
        0 0 2px #00ffff,
        inset 1px 1px 0 rgba(255, 255, 255, 0.3);
    animation: heal-glow 2s ease-in-out infinite alternate;
}

@keyframes heal-glow {
    0% { box-shadow: 0 0 2px #00ffff, inset 1px 1px 0 rgba(255, 255, 255, 0.3); }
    100% { box-shadow: 0 0 4px #00ffff, 0 0 6px #00ffff, inset 1px 1px 0 rgba(255, 255, 255, 0.3); }
}

/* Enhanced Boss Texture */
.minimap-cell.boss {
    background: 
        radial-gradient(circle at 50% 50%, #ff00ff 0%, #cc00cc 30%, #880088 60%, #440044 100%),
        repeating-conic-gradient(from 45deg at 50% 50%, #ff00ff 0deg, #cc00cc 30deg, #880088 60deg, #ff00ff 90deg);
    background-size: 9px 9px, 6px 6px;
    box-shadow: 
        0 0 3px #ff00ff,
        inset 1px 1px 0 rgba(255, 255, 255, 0.2);
    animation: boss-pulse 1.5s ease-in-out infinite alternate;
}

@keyframes boss-pulse {
    0% { 
        box-shadow: 0 0 3px #ff00ff, inset 1px 1px 0 rgba(255, 255, 255, 0.2);
        transform: scale(1);
    }
    100% { 
        box-shadow: 0 0 6px #ff00ff, 0 0 8px #ff00ff, inset 1px 1px 0 rgba(255, 255, 255, 0.2);
        transform: scale(1.1);
    }
}

/* Shop Markers */
.minimap-cell.shop {
    background: 
        radial-gradient(circle at 50% 50%, #00ff00 0%, #00cc00 30%, #008800 60%, #004400 100%),
        repeating-conic-gradient(from 0deg at 50% 50%, #00ff00 0deg, #00cc00 30deg, #00aa00 60deg, #00ff00 90deg);
    background-size: 9px 9px, 6px 6px;
    box-shadow: 
        0 0 3px #00ff00,
        inset 1px 1px 0 rgba(255, 255, 255, 0.3);
    animation: shop-glow 2.5s ease-in-out infinite alternate;
}

.minimap-cell.shop-chapel {
    background:
        radial-gradient(circle at 50% 50%, #FFD700 0%, #FFA500 30%, #FF8C00 60%, #B8860B 100%),
        repeating-conic-gradient(from 0deg at 50% 50%, #FFD700 0deg, #FFA500 30deg, #FF8C00 60deg, #FFD700 90deg);
    background-size: 9px 9px, 6px 6px;
    box-shadow:
        0 0 3px #FFD700,
        inset 1px 1px 0 rgba(255, 255, 255, 0.4);
    animation: chapel-glow 2s ease-in-out infinite alternate;
}

.minimap-cell.shop-alchemist {
    background:
        radial-gradient(circle at 50% 50%, #9370DB 0%, #8A2BE2 30%, #4B0082 60%, #2E0054 100%),
        repeating-conic-gradient(from 0deg at 50% 50%, #9370DB 0deg, #8A2BE2 30deg, #4B0082 60deg, #9370DB 90deg);
    background-size: 9px 9px, 6px 6px;
    box-shadow:
        0 0 3px #9370DB,
        inset 1px 1px 0 rgba(255, 255, 255, 0.4);
    animation: alchemist-glow 2s ease-in-out infinite alternate;
}

@keyframes shop-glow {
    0% { 
        box-shadow: 0 0 3px #00ff00, inset 1px 1px 0 rgba(255, 255, 255, 0.3);
        transform: scale(1);
    }
    100% { 
        box-shadow: 0 0 5px #00ff00, 0 0 7px #00ff00, inset 1px 1px 0 rgba(255, 255, 255, 0.3);
        transform: scale(1.05);
    }
}

@keyframes chapel-glow {
    0% {
        box-shadow: 0 0 3px #FFD700, inset 1px 1px 0 rgba(255, 255, 255, 0.4);
        transform: scale(1);
    }
    100% {
        box-shadow: 0 0 5px #FFD700, 0 0 7px #FFD700, inset 1px 1px 0 rgba(255, 255, 255, 0.4);
        transform: scale(1.05);
    }
}

@keyframes alchemist-glow {
    0% {
        box-shadow: 0 0 3px #9370DB, inset 1px 1px 0 rgba(255, 255, 255, 0.4);
        transform: scale(1);
    }
    100% {
        box-shadow: 0 0 6px #9370DB, 0 0 3px #8A2BE2, inset 1px 1px 0 rgba(255, 255, 255, 0.4);
        transform: scale(1.05);
    }
}

/* Gold Display Styling */
.gold-display {
    position: absolute;
    bottom: 20px;
    left: 20px;
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid #FFD700;
    padding: 10px 15px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

.gold-icon {
    font-size: 20px;
    text-shadow: 0 0 5px #FFD700;
}

.gold-amount {
    color: #FFD700;
    font-family: 'IM Fell English SC', serif;
    font-size: 16px;
    font-weight: bold;
    text-shadow: 0 0 5px #FFD700;
}

#partyGold {
    color: #FFFF00;
    text-shadow: 0 0 3px #FFFF00;
}

/* Shop UI Styling */
.shop-ui {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    z-index: 200;
    justify-content: center;
    align-items: center;
}

.shop-ui.active {
    display: flex;
}

.shop-content {
    background: linear-gradient(145deg, #1f1a12, #2d1f17);
    border: 3px solid #d4af37;
    padding: 30px;
    border-radius: 10px;
    max-width: 700px;
    width: 90%;
    box-shadow: 0 0 20px rgba(212, 175, 55, 0.5);
}

.shop-header {
    text-align: center;
    margin-bottom: 25px;
    position: relative;
}

.shop-header h2 {
    font-size: 24px;
    color: #d4af37;
    text-shadow: 0 0 10px #d4af37;
    margin: 0;
}

.shop-gold-display {
    position: absolute;
    top: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid #FFD700;
    border-radius: 8px;
    padding: 8px 12px;
    font-family: 'IM Fell English SC', serif;
    font-size: 14px;
    color: #FFD700;
    text-shadow: 0 0 5px #FFD700;
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

.shop-gold-display .gold-label {
    margin-right: 5px;
    color: #d4af37;
}

.shop-gold-display .gold-amount {
    font-weight: bold;
    color: #FFD700;
}

.shop-main {
    display: flex;
    gap: 20px;
    align-items: center;
}

.shop-image-container {
    flex-shrink: 0;
    width: 300px;
    height: 300px;
    border: 2px solid #b8860b;
    border-radius: 8px;
    overflow: hidden;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
}

.shop-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    image-rendering: pixelated;
}

.shop-image-fallback {
    display: none;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    text-align: center;
    color: #4CAF50;
}

.shop-details {
    flex: 1;
}

.shop-details h3 {
    font-size: 20px;
    color: #d4af37;
    margin-bottom: 15px;
    text-shadow: 0 0 5px #d4af37;
}

.shop-item-info {
    margin-bottom: 20px;
    font-size: 16px;
    color: #cccccc;
}

.shop-item-info p {
    margin: 8px 0;
}

.shop-item-info span {
    color: #d4af37;
    font-weight: bold;
}

.shop-buttons {
    display: flex;
    gap: 10px;
}

.shop-buttons .action-btn {
    flex: 1;
    padding: 12px;
    font-size: 14px;
}

/* Party Selection UI Styling */
.party-selection-ui {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    z-index: 250;
    justify-content: center;
    align-items: center;
}

.party-selection-ui.active {
    display: flex;
}

.party-selection-content {
    background: linear-gradient(145deg, #1f1a12, #2d1f17);
    border: 3px solid #d4af37;
    padding: 30px;
    border-radius: 10px;
    max-width: 400px;
    width: 90%;
    text-align: center;
    box-shadow: 0 0 20px rgba(212, 175, 55, 0.5);
}

.party-selection-header {
    margin-bottom: 20px;
}

.party-selection-header h3 {
    font-size: 20px;
    color: #d4af37;
    margin-bottom: 10px;
    text-shadow: 0 0 5px #d4af37;
}

.party-selection-header p {
    font-size: 14px;
    color: #cccccc;
    margin: 0;
}

.party-selection-members {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 20px;
}

.party-member-btn {
    padding: 15px;
    background: linear-gradient(145deg, #3d2f1f, #5c4a35);
    border: 2px solid #b8860b;
    color: #d4af37;
    font-family: 'IM Fell English SC', serif;
    font-size: 14px;
    cursor: pointer;
    border-radius: 5px;
    transition: all 0.2s;
    text-align: left;
}

.party-member-btn:hover {
    background: linear-gradient(145deg, #5c4a35, #8b6b47);
    box-shadow: 0 0 15px #d4af37;
}

.party-member-btn:disabled {
    background: #333;
    color: #666;
    cursor: not-allowed;
    box-shadow: none;
}

.party-member-name {
    font-weight: bold;
    margin-bottom: 5px;
    color: #d4af37;
}

.party-member-stats {
    font-size: 12px;
    color: #cccccc;
}

.party-member-btn.dead {
    background: rgba(100, 0, 0, 0.3);
    border-color: #660000;
    color: #ff4444;
}

.party-member-btn.dead .party-member-name {
    color: #ff4444;
}

/* Skill Selection UI */
.skill-selection-ui {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(145deg, #2c1810, #4a3020);
    border: 3px solid #d4af37;
    border-radius: 10px;
    padding: 20px;
    z-index: 1001;
    min-width: 400px;
    max-width: 500px;
    box-shadow: 0 0 30px rgba(212, 175, 55, 0.3);
}

.skill-selection-content {
    color: #d4af37;
    font-family: 'IM Fell English SC', serif;
}

.skill-selection-header {
    text-align: center;
    margin-bottom: 20px;
    border-bottom: 2px solid #d4af37;
    padding-bottom: 10px;
}

.skill-selection-header h3 {
    margin: 0 0 10px 0;
    font-size: 20px;
    color: #ffff66;
}

.skill-selection-header p {
    margin: 0;
    font-size: 14px;
    color: #ccc;
}

.skill-selection-list {
    max-height: 300px;
    overflow-y: auto;
    margin-bottom: 20px;
}

.skill-option {
    background: linear-gradient(145deg, #3d2f1f, #5c4a35);
    border: 2px solid #8b6b47;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.2s;
}

.skill-option:hover {
    background: linear-gradient(145deg, #5c4a35, #8b6b47);
    border-color: #d4af37;
    box-shadow: 0 0 15px rgba(212, 175, 55, 0.3);
}

.skill-option.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: linear-gradient(145deg, #2a2a2a, #3a3a3a);
    border-color: #666;
}

.skill-option.disabled:hover {
    background: linear-gradient(145deg, #2a2a2a, #3a3a3a);
    border-color: #666;
    box-shadow: none;
}

.skill-name {
    font-size: 16px;
    font-weight: bold;
    color: #ffff66;
    margin-bottom: 5px;
}

.skill-description {
    font-size: 12px;
    color: #ccc;
    line-height: 1.4;
}

.skill-level {
    font-size: 10px;
    color: #d4af37;
    margin-top: 5px;
}

.skill-selection-buttons {
    text-align: center;
    border-top: 2px solid #d4af37;
    padding-top: 15px;
}

/* Target Selection UI (for First Aid) */
.target-selection-ui {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(145deg, #2c1810, #4a3020);
    border: 3px solid #d4af37;
    border-radius: 10px;
    padding: 20px;
    z-index: 1002;
    min-width: 400px;
    max-width: 500px;
    box-shadow: 0 0 30px rgba(212, 175, 55, 0.3);
}

.target-selection-content {
    color: #d4af37;
    font-family: 'IM Fell English SC', serif;
}

.target-selection-header {
    text-align: center;
    margin-bottom: 20px;
    border-bottom: 2px solid #d4af37;
    padding-bottom: 10px;
}

.target-selection-header h3 {
    margin: 0 0 10px 0;
    font-size: 20px;
    color: #66ff66;
}

.target-selection-header p {
    margin: 0;
    font-size: 14px;
    color: #ccc;
}

.target-selection-list {
    max-height: 300px;
    overflow-y: auto;
    margin-bottom: 20px;
}

.target-option {
    background: linear-gradient(145deg, #3d2f1f, #5c4a35);
    border: 2px solid #8b6b47;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.target-option:hover {
    background: linear-gradient(145deg, #5c4a35, #8b6b47);
    border-color: #66ff66;
    box-shadow: 0 0 15px rgba(102, 255, 102, 0.3);
}

.target-option.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: linear-gradient(145deg, #2a2a2a, #3a3a3a);
    border-color: #666;
}

.target-option.disabled:hover {
    background: linear-gradient(145deg, #2a2a2a, #3a3a3a);
    border-color: #666;
    box-shadow: none;
}

.target-option.dead {
    background: linear-gradient(145deg, #3a1a1a, #4a2a2a);
    border-color: #664444;
    opacity: 0.6;
}

.target-name {
    font-size: 16px;
    font-weight: bold;
    color: #66ff66;
    margin-bottom: 5px;
}

.target-option.dead .target-name {
    color: #ff4444;
}

.target-stats {
    font-size: 12px;
    color: #ccc;
    line-height: 1.4;
}

.target-option.dead .target-stats {
    color: #888;
}

.target-health {
    font-size: 14px;
    color: #ffff66;
    margin-top: 3px;
}

.target-option.dead .target-health {
    color: #ff6666;
}

.target-selection-buttons {
    text-align: center;
    border-top: 2px solid #d4af37;
    padding-top: 15px;
}

/* Item UI Styling */
.item-ui {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    z-index: 200;
    justify-content: center;
    align-items: center;
}

.item-ui.active {
    display: flex;
}

.item-content {
    background: linear-gradient(145deg, #1f1a12, #2d1f17);
    border: 3px solid #d4af37;
    padding: 30px;
    border-radius: 10px;
    max-width: 500px;
    width: 90%;
    text-align: center;
    box-shadow: 0 0 20px rgba(212, 175, 55, 0.5);
}

.item-header {
    margin-bottom: 20px;
}

.item-header h3 {
    font-size: 20px;
    color: #d4af37;
    margin-bottom: 10px;
    text-shadow: 0 0 5px #d4af37;
}

.item-header p {
    font-size: 14px;
    color: #cccccc;
    margin: 0;
}

.item-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 20px;
    max-height: 300px;
    overflow-y: auto;
}

.item-btn {
    padding: 15px;
    background: linear-gradient(145deg, #3d2f1f, #5c4a35);
    border: 2px solid #b8860b;
    color: #d4af37;
    font-family: 'IM Fell English SC', serif;
    font-size: 14px;
    cursor: pointer;
    border-radius: 5px;
    transition: all 0.2s;
    text-align: left;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.item-btn:hover {
    background: linear-gradient(145deg, #5c4a35, #8b6b47);
    box-shadow: 0 0 15px #d4af37;
    transform: translateY(-2px);
}

.item-name {
    font-weight: bold;
    margin-bottom: 5px;
    color: #d4af37;
    font-size: 16px;
}

.item-description {
    font-size: 12px;
    color: #cccccc;
    margin-bottom: 5px;
    line-height: 1.3;
}

.item-count {
    font-size: 12px;
    color: #ffff66;
    font-weight: bold;
    align-self: flex-end;
    margin-top: 5px;
}

/* Empty item list message */
.item-list:empty::before {
    content: "No items available";
    color: #888;
    font-style: italic;
    text-align: center;
    padding: 20px;
    display: block;
}

/* Scrollbar styling for item list */
.item-list::-webkit-scrollbar {
    width: 8px;
}

.item-list::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 4px;
}

.item-list::-webkit-scrollbar-thumb {
    background: #b8860b;
    border-radius: 4px;
}

.item-list::-webkit-scrollbar-thumb:hover {
    background: #d4af37;
}

/* Enhanced Shop UI Styling */
.shop-menu {
    width: 100%;
}

/* Alchemist Shop Styling */
.alchemist-skill-list {
    max-height: 300px;
    overflow-y: auto;
    margin: 15px 0;
}

.alchemist-skill-option {
    background: rgba(0, 0, 0, 0.7);
    border: 1px solid #9370DB;
    border-radius: 5px;
    padding: 10px;
    margin: 8px 0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.alchemist-skill-option:hover {
    background: rgba(147, 112, 219, 0.2);
    border-color: #BA55D3;
    transform: translateY(-2px);
}

.alchemist-skill-option.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: rgba(0, 0, 0, 0.5);
    pointer-events: none;
}

.alchemist-skill-option.disabled:hover {
    transform: none;
    background: rgba(0, 0, 0, 0.5);
}

.skill-name {
    color: #9370DB;
    font-weight: bold;
    font-size: 14px;
    margin-bottom: 5px;
}

.skill-description {
    color: #cccccc;
    font-size: 12px;
    margin-bottom: 5px;
}

.skill-cost {
    color: #FFD700;
    font-size: 11px;
    font-weight: bold;
}

/* Skill Equipment Interface Styling */
.skill-equipment-ui {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
}

.skill-equipment-content {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    border: 2px solid #4a90e2;
    border-radius: 10px;
    padding: 20px;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 0 20px rgba(74, 144, 226, 0.5);
}

.skill-equipment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    border-bottom: 1px solid #4a90e2;
    padding-bottom: 10px;
}

.skill-equipment-header h3 {
    color: #4a90e2;
    margin: 0;
    font-size: 24px;
    text-shadow: 0 0 5px #4a90e2;
}

.close-btn {
    background: #ff4444;
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: #ff6666;
    transform: scale(1.1);
}

.character-selection h4,
.skill-equipment-panel h4,
.skill-equipment-panel h5 {
    color: #4a90e2;
    margin: 15px 0 10px 0;
    text-shadow: 0 0 3px #4a90e2;
}

.character-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    margin-bottom: 20px;
}

.character-option {
    background: rgba(0, 0, 0, 0.7);
    border: 1px solid #4a90e2;
    border-radius: 5px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.character-option:hover {
    background: rgba(74, 144, 226, 0.2);
    border-color: #66a3ff;
    transform: translateY(-2px);
}

.character-name {
    color: #4a90e2;
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 5px;
}

.character-class {
    color: #cccccc;
    font-size: 14px;
    margin-bottom: 5px;
}

.equipped-count {
    color: #FFD700;
    font-size: 12px;
    font-weight: bold;
}

.selected-character-info {
    background: rgba(0, 0, 0, 0.5);
    border: 1px solid #4a90e2;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 20px;
}

.character-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    margin-top: 10px;
}

.character-stats div {
    color: #cccccc;
    font-size: 14px;
    text-align: center;
    padding: 5px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 3px;
}

.equipped-skills-list,
.available-skills-list {
    max-height: 200px;
    overflow-y: auto;
    margin-bottom: 15px;
}

.equipped-skill,
.available-skill {
    background: rgba(0, 0, 0, 0.7);
    border: 1px solid #4a90e2;
    border-radius: 5px;
    padding: 10px;
    margin: 8px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.available-skill.disabled {
    opacity: 0.5;
    border-color: #666;
}

.equipped-skill .skill-name,
.available-skill .skill-name {
    color: #4a90e2;
    font-weight: bold;
    font-size: 14px;
}

.equipped-skill .skill-description,
.available-skill .skill-description {
    color: #cccccc;
    font-size: 12px;
    margin-top: 3px;
}

.equip-btn,
.unequip-btn {
    background: #4a90e2;
    color: white;
    border: none;
    border-radius: 3px;
    padding: 5px 10px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.equip-btn:hover,
.unequip-btn:hover {
    background: #66a3ff;
    transform: scale(1.05);
}

.unequip-btn {
    background: #ff6666;
}

.unequip-btn:hover {
    background: #ff8888;
}

.max-skills {
    color: #ff6666;
    font-size: 12px;
    font-style: italic;
}

.no-skills {
    color: #888;
    font-style: italic;
    text-align: center;
    padding: 20px;
}

.skill-equipment-buttons {
    text-align: center;
    margin-top: 20px;
    border-top: 1px solid #4a90e2;
    padding-top: 15px;
}

.shop-option-buttons {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 20px;
}

.shop-option-btn {
    padding: 20px;
    background: linear-gradient(145deg, #3d2f1f, #5c4a35);
    border: 2px solid #b8860b;
    color: #d4af37;
    font-family: 'IM Fell English SC', serif;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.3s;
    text-align: left;
    width: 100%;
}

.shop-option-btn:hover {
    background: linear-gradient(145deg, #5c4a35, #8b6b47);
    box-shadow: 0 0 15px rgba(212, 175, 55, 0.4);
    transform: translateY(-2px);
}

.option-title {
    font-size: 18px;
    font-weight: bold;
    color: #d4af37;
    margin-bottom: 8px;
    text-shadow: 0 0 3px #d4af37;
}

.option-description {
    font-size: 14px;
    color: #cccccc;
    line-height: 1.3;
}

.shop-item-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 20px;
}

.shop-item-option {
    padding: 15px;
    background: linear-gradient(145deg, #2d1f17, #3d2f1f);
    border: 2px solid #8b4513;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s;
    position: relative;
}

.shop-item-option:hover {
    background: linear-gradient(145deg, #3d2f1f, #5c4a35);
    border-color: #d4af37;
    box-shadow: 0 0 12px rgba(212, 175, 55, 0.3);
    transform: translateY(-1px);
}

.item-option-name {
    font-size: 16px;
    font-weight: bold;
    color: #d4af37;
    margin-bottom: 6px;
    text-shadow: 0 0 2px #d4af37;
}

.item-option-description {
    font-size: 13px;
    color: #cccccc;
    margin-bottom: 8px;
    line-height: 1.4;
}

.item-option-price {
    font-size: 14px;
    color: #ffff66;
    font-weight: bold;
    position: absolute;
    top: 15px;
    right: 15px;
    text-shadow: 0 0 2px #ffff66;
}

/* Shop menu transitions */
.shop-menu {
    opacity: 1;
    transition: opacity 0.2s ease-in-out;
}

.shop-menu[style*="display: none"] {
    opacity: 0;
}

/* Responsive adjustments for combat UI */
@media (max-width: 1200px) {
    .combat-layout {
        grid-template-columns: 1.2fr 2fr 1.2fr;
    }

    .enemy-area-left,
    .enemy-area-right {
        min-height: 650px;
    }

    .enemy-card {
        min-height: 570px;
        min-width: 240px;
        max-width: 320px;
        padding: 18px;
    }

    .enemy-image-container {
        width: 240px;
        height: 380px;
        margin-bottom: 18px;
    }

    .combat-party-members {
        flex-wrap: wrap;
    }

    .combat-party-card {
        min-width: 100px;
    }
}

@media (max-width: 900px) {
    .combat-layout {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto;
        gap: 15px;
        padding: 15px;
    }

    .enemy-area-left,
    .enemy-area-right {
        min-height: 400px;
        order: 1;
    }

    .enemy-area-right {
        order: 3;
    }

    .combat-center {
        order: 2;
        max-height: 500px;
    }

    .enemy-card {
        min-height: 380px;
        min-width: 200px;
        max-width: 280px;
        padding: 15px;
    }

    .enemy-image-container {
        width: 180px;
        height: 300px;
        margin-bottom: 15px;
    }

    .combat-party-members {
        flex-wrap: wrap;
        justify-content: center;
    }

    .combat-party-card {
        min-width: 90px;
        flex: 0 1 calc(50% - 4px);
    }
}

@media (max-width: 600px) {
    .enemy-area-left,
    .enemy-area-right {
        min-height: 350px;
    }

    .enemy-card {
        min-height: 320px;
        min-width: 160px;
        max-width: 220px;
        padding: 12px;
    }

    .enemy-image-container {
        width: 140px;
        height: 260px;
        margin-bottom: 12px;
    }

    .enemy-name {
        font-size: 16px;
    }

    .enemy-stats {
        font-size: 11px;
    }

    .enemy-status {
        font-size: 12px;
        padding: 4px 8px;
    }

    .combat-party-card {
        min-width: 80px;
        flex: 0 1 calc(33.333% - 6px);
    }

    .combat-actions {
        grid-template-columns: 1fr;
        grid-template-rows: repeat(6, 1fr);
    }
}

/* Responsive adjustments for shop UI */
@media (max-width: 800px) {
    .shop-content {
        max-width: 95%;
        padding: 20px;
    }

    .shop-main {
        flex-direction: column;
        gap: 15px;
    }

    .shop-image-container {
        width: 200px;
        height: 200px;
        align-self: center;
    }

    .option-title {
        font-size: 16px;
    }

    .option-description {
        font-size: 12px;
    }
}

/* Orb Modal Styling */
.orb-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    z-index: 1000;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
}

.orb-modal.active {
    display: flex;
}

.orb-content {
    background: linear-gradient(145deg, #2e1a2e, #3e1640);
    border: 3px solid #8e44ad;
    border-radius: 15px;
    padding: 40px;
    text-align: center;
    max-width: 500px;
    width: 90%;
    box-shadow: 0 0 30px rgba(142, 68, 173, 0.8);
    position: relative;
    transform: scale(0.9);
    transition: transform 0.3s ease-in-out;
}

.orb-modal.active .orb-content {
    transform: scale(1);
}

.orb-title {
    font-size: 24px;
    font-weight: bold;
    color: #bb86fc;
    margin-bottom: 20px;
    text-shadow: 0 0 10px #bb86fc;
    letter-spacing: 2px;
    text-transform: uppercase;
}

.orb-image {
    max-width: 100%;
    max-height: 300px;
    width: auto;
    height: auto;
    margin: 20px auto;
    display: block;
    filter: drop-shadow(0 0 15px #bb86fc);
    animation: float 3s ease-in-out infinite;
    object-fit: contain;
}

.orb-description {
    font-size: 16px;
    color: #e1bee7;
    margin: 20px 0;
    line-height: 1.6;
    text-shadow: 0 0 5px rgba(187, 134, 252, 0.3);
}

.orb-dismiss {
    font-size: 12px;
    color: #ce93d8;
    margin-top: 30px;
    opacity: 0.8;
    animation: blink 2s ease-in-out infinite;
}

/* Mystic Orb Modal Styling */
.mystic-orb-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
}

.mystic-orb-modal.active {
    display: flex;
}

.mystic-orb-content {
    background: linear-gradient(145deg, #1a0d2e, #2d1b3e);
    border: 3px solid #9c27b0;
    border-radius: 15px;
    padding: 40px;
    text-align: center;
    max-width: 400px;
    width: 90%;
    box-shadow: 0 0 30px rgba(156, 39, 176, 0.8);
    position: relative;
    transform: scale(0.9);
    transition: transform 0.3s ease-in-out;
}

.mystic-orb-modal.active .mystic-orb-content {
    transform: scale(1);
}

.mystic-orb-title {
    font-size: 24px;
    font-weight: bold;
    color: #e1bee7;
    margin-bottom: 20px;
    text-shadow: 0 0 15px #9c27b0;
    letter-spacing: 2px;
    text-transform: uppercase;
}

.mystic-orb-image {
    max-width: 100%;
    max-height: 250px;
    width: auto;
    height: auto;
    margin: 20px auto;
    display: block;
    filter: drop-shadow(0 0 20px #9c27b0);
    animation: float 3s ease-in-out infinite, glow 2s ease-in-out infinite alternate;
    object-fit: contain;
}

.mystic-orb-description {
    font-size: 16px;
    color: #f3e5f5;
    margin: 20px 0;
    line-height: 1.6;
    text-shadow: 0 0 8px rgba(156, 39, 176, 0.4);
}

.mystic-orb-dismiss {
    font-size: 12px;
    color: #e1bee7;
    margin-top: 30px;
    opacity: 0.8;
    animation: blink 2s ease-in-out infinite;
}

@keyframes glow {
    from {
        filter: drop-shadow(0 0 20px #9c27b0);
    }
    to {
        filter: drop-shadow(0 0 30px #e1bee7);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes blink {
    0%, 100% {
        opacity: 0.8;
    }
    50% {
        opacity: 0.3;
    }
}

/* Trap Pit Modal Styling */
.trap-pit-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    z-index: 10000;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
}

.trap-pit-modal.active {
    display: flex;
}

.trap-pit-content {
    background: linear-gradient(145deg, #2d1a0f, #3e2a1a);
    border: 3px solid #ff6b47;
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    max-width: 500px;
    width: 90%;
    box-shadow: 0 10px 30px rgba(255, 107, 71, 0.3);
    position: relative;
    transform: scale(0.9);
    transition: transform 0.3s ease-in-out;
}

.trap-pit-modal.active .trap-pit-content {
    transform: scale(1);
}

.trap-pit-title {
    font-size: 24px;
    font-weight: bold;
    color: #ff6b47;
    margin-bottom: 20px;
    text-shadow: 0 0 10px #ff6b47;
    font-family: 'Courier New', monospace;
}

.trap-pit-image {
    max-width: 90vw;
    max-height: 70vh;
    object-fit: contain;
    margin: 20px auto;
    display: block;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(255, 107, 71, 0.4);
    filter: drop-shadow(0 0 10px #ff6b47);
}

.trap-pit-messages {
    color: #ffccaa;
    font-size: 16px;
    line-height: 1.6;
    margin: 20px 0;
    font-family: 'Courier New', monospace;
}

.trap-pit-message-line {
    margin: 8px 0;
    padding: 5px;
    background: rgba(255, 107, 71, 0.1);
    border-radius: 5px;
    border-left: 3px solid #ff6b47;
}

.trap-pit-dismiss {
    color: #ff9980;
    font-size: 12px;
    margin-top: 20px;
    font-style: italic;
    opacity: 0.8;
    font-family: 'Courier New', monospace;
}

/* Wild Meat Modal Styling */
.wild-meat-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    z-index: 10000;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
}

.wild-meat-modal.active {
    display: flex;
}

.wild-meat-content {
    background: linear-gradient(145deg, #2d1a0f, #3e2a1a);
    border: 3px solid #8b4513;
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    color: #f4e4bc;
    max-width: 400px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    position: relative;
    transform: scale(0.9);
    transition: transform 0.3s ease-in-out;
}

.wild-meat-modal.active .wild-meat-content {
    transform: scale(1);
}

.wild-meat-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #8b4513;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    font-family: 'Courier New', monospace;
}

.wild-meat-image {
    max-width: 90vw;
    max-height: 70vh;
    margin: 20px auto;
    display: block;
    border-radius: 10px;
    border: 2px solid #8b4513;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    object-fit: contain;
}

.wild-meat-description {
    font-size: 16px;
    line-height: 1.6;
    margin: 20px 0;
    color: #f4e4bc;
    font-family: 'Courier New', monospace;
}

.wild-meat-dismiss {
    color: #cd853f;
    font-size: 12px;
    margin-top: 20px;
    font-style: italic;
    opacity: 0.8;
    font-family: 'Courier New', monospace;
}
