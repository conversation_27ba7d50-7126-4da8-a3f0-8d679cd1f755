# DungeonRPG プロジェクト構成

## ディレクトリ構造

```
DungeonRPG/
├── assets/                     # ゲームアセット
│   ├── audio/                  # 音声ファイル
│   │   ├── Barbarian Attack.m4a    # 戦闘BGM
│   │   ├── Notturno.m4a           # 探索BGM (フロア4-6)
│   │   ├── Overture.m4a           # タイトル画面BGM
│   │   ├── Prelude To Battle.m4a  # 戦闘前BGM
│   │   ├── Profundo.m4a           # 探索BGM (フロア7-9)
│   │   ├── The Fall of Love.m4a   # 探索BGM (フロア1-3)
│   │   ├── The Search.m4a         # 探索BGM (フロア10)
│   │   ├── blow.mp3               # 魔法使いの物理攻撃音
│   │   ├── breath.mp3             # ブレス攻撃音
│   │   ├── button.mp3             # ボタンクリック音
│   │   ├── criticaldamage.mp3     # クリティカルダメージ音
│   │   ├── damage.mp3             # モンスターの物理攻撃音
│   │   ├── defeat.mp3             # モンスター撃破音
│   │   ├── descending.mp3         # 階段下降音
│   │   ├── healingpoint.mp3       # 回復ポイント使用音
│   │   ├── magic.mp3              # 魔法攻撃音
│   │   ├── opendoor.mp3           # ドア開放音
│   │   ├── poisonattack.mp3       # 毒攻撃音
│   │   ├── recover.mp3            # レンジャーの応急手当音
│   │   ├── shockwave.mp3          # 衝撃波音
│   │   └── slash.mp3              # 戦士・レンジャーの物理攻撃音
│   └── images/                 # 画像ファイル
│       ├── Combat trainer.png      # 戦闘訓練師
│       ├── HealingPoint.png        # 回復ポイント
│       ├── Magical shop.png        # 魔法店
│       ├── MagicalMappingTool.png  # 魔法の地図ツール
│       ├── Martial artist.png      # 武術家
│       ├── Merchant.png            # 商人
│       ├── MireStalker.png         # ミア・ストーカー
│       ├── Priest.png              # 司祭
│       ├── alchemist1.png          # 錬金術師1
│       ├── alchemist2.png          # 錬金術師2
│       ├── ancient evil.png        # 古代の悪
│       ├── angel.png               # 天使
│       ├── antidote.png            # 解毒剤
│       ├── assassin master.webp    # 暗殺者マスター
│       ├── assassin.png            # 暗殺者
│       ├── assassin2.png           # 暗殺者2
│       ├── castle.png              # 城
│       ├── combat.png              # 戦闘背景
│       ├── corridor.png            # 廊下
│       ├── cyclops.png             # サイクロプス
│       ├── dark angel.png          # ダークエンジェル
│       ├── dark load.png           # ダークロード
│       ├── demon elite.png         # エリートデーモン
│       ├── demon.png               # デーモン
│       ├── devil.png               # デビル
│       ├── dragon.png              # ドラゴン
│       ├── dungeon.png             # ダンジョン
│       ├── elite monster.png       # エリートモンスター
│       ├── enchanted room.png      # 魔法の部屋
│       ├── evil mage.png           # 邪悪な魔法使い
│       ├── evil magician.png       # 邪悪な魔術師
│       ├── evil priest.png         # 邪悪な司祭
│       ├── fiend.png               # フィーンド
│       ├── fire giant.png          # ファイアジャイアント
│       ├── ghost.png               # ゴースト
│       ├── ghoul.png               # グール
│       ├── giant orc.png           # ジャイアントオーク
│       ├── giant spider.png        # ジャイアントスパイダー
│       ├── giant troll.png         # ジャイアントトロール
│       ├── giant.png               # ジャイアント
│       ├── goblin.png              # ゴブリン
│       ├── golem.png               # ゴーレム
│       ├── greater demon.png       # グレーターデーモン
│       ├── green dragon.png        # グリーンドラゴン
│       ├── heal potion.png         # 回復ポーション
│       ├── hell hound.png          # ヘルハウンド
│       ├── imp.png                 # インプ
│       ├── killer wolf.png         # キラーウルフ
│       ├── lesser demon.png        # レッサーデーモン
│       ├── map menu.png            # マップメニュー
│       ├── ninja.png               # 忍者
│       ├── orb special.png         # 特別なオーブ
│       ├── orb.png                 # オーブ
│       ├── orc.png                 # オーク
│       ├── phantom.png             # ファントム
│       ├── pitfall.png             # 落とし穴
│       ├── red dragon.png          # レッドドラゴン
│       ├── rogue.png               # ローグ
│       ├── room.png                # 部屋
│       ├── skelton knight.png      # スケルトンナイト
│       ├── slime.png               # スライム
│       ├── smoke screen.png        # スモークスクリーン
│       ├── snake.png               # スネーク
│       ├── stalker.png             # ストーカー
│       ├── stone golem.png         # ストーンゴーレム
│       ├── thief.png               # シーフ
│       ├── tiger.png               # タイガー
│       ├── title.png               # タイトル画面
│       ├── town map.png            # 町の地図
│       ├── treasurebox.png         # 宝箱
│       ├── tree house.png          # ツリーハウス
│       ├── troll.png               # トロール
│       ├── vampire lord.png        # ヴァンパイアロード
│       ├── vampire.png             # ヴァンパイア
│       ├── victory.png             # 勝利画面
│       ├── wild meat.png           # 野生の肉
│       ├── willowisp.png           # ウィルオウィスプ
│       ├── wing rizard.png         # ウィングリザード
│       ├── yellow dragon.png       # イエロードラゴン
│       └── zombie.png              # ゾンビ
├── docs/                       # ドキュメント
│   └── context.md              # プロジェクト構成とコンテキスト
├── js/                         # JavaScriptソースコード
│   ├── core/                   # コアシステム
│   │   ├── Constants.js        # ゲーム定数とモンスター定義
│   │   ├── GameCore.js         # ゲームコア機能
│   │   └── GameState.js        # ゲーム状態管理
│   ├── main.js                 # メインエントリーポイント
│   ├── systems/                # ゲームシステム
│   │   ├── Character.js        # キャラクター管理
│   │   ├── Combat.js           # 戦闘システム
│   │   ├── Dungeon.js          # ダンジョン生成・管理
│   │   ├── Player.js           # プレイヤー管理
│   │   ├── Renderer.js         # レンダリングシステム
│   │   └── SoundManager.js     # 音声管理
│   ├── ui/                     # ユーザーインターフェース
│   │   ├── AntidoteNotification.js         # 解毒剤通知
│   │   ├── FloorProgressionNotification.js # フロア進行通知
│   │   ├── HealingPointNotification.js     # 回復ポイント通知
│   │   ├── HealthPotionNotification.js     # 回復ポーション通知
│   │   ├── InputHandler.js                 # 入力処理
│   │   ├── MappingToolNotification.js      # マッピングツール通知
│   │   ├── MysticOrbNotification.js        # ミスティックオーブ通知
│   │   ├── OptionScreen.js                 # オプション画面
│   │   ├── OrbNotification.js              # オーブ通知
│   │   ├── SmokeScreenNotification.js      # スモークスクリーン通知
│   │   ├── TitleScreen.js                  # タイトル画面
│   │   ├── TrapPitNotification.js          # 落とし穴トラップ通知
│   │   ├── UIManager.js                    # UI管理
│   │   └── WildMeatNotification.js         # 野生の肉通知
│   └── utils/                  # ユーティリティ
│       └── ImageManager.js     # 画像管理
├── baseStats.txt               # 基本ステータス情報
├── index.html                  # メインHTMLファイル
└── style.css                   # スタイルシート
```

## プロジェクト概要

DungeonRPGは、ブラウザベースのダンジョン探索RPGゲームです。プレイヤーは戦士、魔法使い、レンジャーの3つのクラスから選択し、10階層のダンジョンを探索します。

### 主要機能

- **3Dダンジョン表示**: 一人称視点での3D廊下表示
- **戦闘システム**: ターンベースの戦闘
- **音響システム**: 背景音楽と効果音の動的切り替え
- **視覚効果**: ダークファンタジー雰囲気の演出
- **アイテムシステム**: 回復アイテム、特殊アイテム
- **ショップシステム**: 各種店舗での取引
- **スキルシステム**: レベルアップによる特殊能力習得

### 技術仕様

- **フロントエンド**: HTML5 Canvas, JavaScript ES6+
- **音声**: HTML5 Audio API
- **解像度**: 800x600 固定
- **ブラウザ対応**: モダンブラウザ（Chrome, Firefox, Safari, Edge）

### Audio System
- The game already has a working audio system for combat sounds and background music
- Sound effects should be triggered at the exact moment the corresponding game event occurs
- Maintain the existing audio system's browser compatibility and fallback mechanisms

### Code Quality 
   - Maintain all existing file dependencies and game functionality
   - Use minimal code changes by leveraging the existing game mechanisms

### Testing and Cleanup
- If any temporary test files are created during development, delete them after task completion
- Verify that the newly created codes don't conflict with existing systems

### Monster Implementation Requirements
- Integrate the monster definition into the existing Constants.js file following the same structure and naming conventions as other monsters
- Ensure the monster follows established patterns for the existing monster types
- Make minimal code changes while preserving all existing functionality
- Maintain compatibility with the Renderer.js system and combat mechanics
- Verify the monster integrates properly with the existing spawn system and floor-based encounter mechanics