// ===== HEALING POINT USAGE NOTIFICATION =====
// Displays an overlay when a healing point is used

class HealingPointNotification {
    constructor() {
        this.modal = null;
        this.isShowing = false;
        this.imageLoaded = false;
        this.init();
    }
    
    init() {
        // Get the modal element
        this.modal = document.getElementById('healingPointModal');
        if (!this.modal) {
            console.error('Healing point modal not found in DOM');
            return;
        }
        
        // Set up click handler to dismiss the notification
        this.modal.addEventListener('click', () => {
            this.dismissNotification();
        });
        
        // Preload the healing point image
        this.preloadImage();
        
        console.log('Healing point notification system initialized');
    }
    
    // Preload the healing point image
    async preloadImage() {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => {
                this.imageLoaded = true;
                console.log('Healing point image preloaded successfully');
                resolve();
            };
            img.onerror = () => {
                console.error('Failed to preload healing point image');
                reject();
            };
            img.src = 'assets/images/HealingPoint.png';
        });
    }
    
    // Show the healing point notification
    async showNotification() {
        if (!this.modal || this.isShowing) return;
        
        console.log('Showing healing point notification');
        
        // Ensure the image is loaded before showing
        if (!this.imageLoaded) {
            await this.preloadImage();
        }
        
        // Show the modal
        this.modal.classList.add('active');
        this.isShowing = true;
        
        // Add a slight delay for better visual effect
        setTimeout(() => {
            if (this.modal) {
                this.modal.style.opacity = '1';
            }
        }, 50);
        
        // Auto-dismiss after 5 seconds if user doesn't click
        setTimeout(() => {
            if (this.isShowing) {
                this.dismissNotification();
            }
        }, 5000);
    }
    
    // Dismiss the notification
    dismissNotification() {
        if (!this.modal || !this.isShowing) return;
        
        console.log('Dismissing healing point notification');
        
        // Fade out effect
        this.modal.style.opacity = '0';
        
        // Hide the modal after fade out
        setTimeout(() => {
            if (this.modal) {
                this.modal.classList.remove('active');
                this.isShowing = false;
            }
        }, 300);
    }
    
    // Check if notification is currently showing
    isNotificationShowing() {
        return this.isShowing;
    }

    // Debug method to test the notification
    testNotification() {
        console.log('Testing healing point notification...');
        this.showNotification();
    }
}

// Create global instance
const healingPointNotification = new HealingPointNotification();

// Make available globally for browser
if (typeof window !== 'undefined') {
    window.healingPointNotification = healingPointNotification;
}
