// ===== MAPPING TOOL NOTIFICATION SYSTEM =====
// Handles visual notifications when the mapping tool is acquired

class MappingToolNotification {
    constructor() {
        this.modal = null;
        this.isShowing = false;
        this.initialized = false;
    }
    
    // Initialize the notification system
    initialize() {
        if (this.initialized) return;
        
        this.modal = document.getElementById('mappingToolModal');
        if (!this.modal) {
            console.error('Mapping tool modal not found in DOM');
            return;
        }
        
        // Setup click handler for dismissal
        this.setupEventListeners();
        
        // Listen for mapping tool acquisition events
        if (gameState) {
            gameState.addEventListener(CONSTANTS.EVENTS.MAPPING_TOOL_OBTAINED, () => {
                console.log('Mapping tool obtained event received, showing notification...');
                this.showNotification();
            });
        }
        
        this.initialized = true;
        console.log('Mapping Tool Notification system initialized');
    }
    
    // Setup event listeners for modal interaction
    setupEventListeners() {
        if (!this.modal) return;
        
        // Click anywhere on modal to dismiss
        this.modal.addEventListener('click', (event) => {
            this.dismissNotification();
        });
        
        // Prevent event bubbling on content clicks
        const content = this.modal.querySelector('.mapping-tool-content');
        if (content) {
            content.addEventListener('click', (event) => {
                // Still allow dismissal when clicking on content
                this.dismissNotification();
            });
        }
        
        // ESC key to dismiss
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && this.isShowing) {
                this.dismissNotification();
            }
        });
    }
    
    // Show the mapping tool notification
    async showNotification() {
        if (!this.modal || this.isShowing) return;
        
        console.log('Showing mapping tool notification');
        
        // Ensure the image is loaded before showing
        await this.preloadImage();
        
        // Show the modal
        this.modal.classList.add('active');
        this.isShowing = true;
        
        // Add a slight delay for better visual effect
        setTimeout(() => {
            if (this.modal) {
                this.modal.style.opacity = '1';
            }
        }, 50);
        
        // Auto-dismiss after 10 seconds if user doesn't click
        setTimeout(() => {
            if (this.isShowing) {
                this.dismissNotification();
            }
        }, 10000);
    }
    
    // Dismiss the notification
    dismissNotification() {
        if (!this.modal || !this.isShowing) return;
        
        console.log('Dismissing mapping tool notification');
        
        // Fade out effect
        this.modal.style.opacity = '0';
        
        // Remove modal after fade
        setTimeout(() => {
            if (this.modal) {
                this.modal.classList.remove('active');
                this.modal.style.opacity = '';
            }
            this.isShowing = false;
        }, 200);
    }
    
    // Preload the mapping tool image
    async preloadImage() {
        const imagePath = 'assets/images/MagicalMappingTool.png';
        
        if (imageManager) {
            try {
                const result = await imageManager.preloadImage(imagePath);
                if (!result.success) {
                    console.warn('Failed to preload mapping tool image:', imagePath);
                }
                return result;
            } catch (error) {
                console.error('Error preloading mapping tool image:', error);
                return { success: false, error };
            }
        } else {
            // Fallback if imageManager is not available
            return new Promise((resolve) => {
                const img = new Image();
                img.onload = () => resolve({ success: true });
                img.onerror = () => resolve({ success: false });
                img.src = imagePath;
            });
        }
    }
    
    // Check if notification is currently showing
    isNotificationShowing() {
        return this.isShowing;
    }
    
    // Force hide notification (for emergency cases)
    forceHide() {
        if (this.modal) {
            this.modal.classList.remove('active');
            this.modal.style.opacity = '';
        }
        this.isShowing = false;
    }
}

// Create global instance
const mappingToolNotification = new MappingToolNotification();

// Make available globally for browser
if (typeof window !== 'undefined') {
    window.mappingToolNotification = mappingToolNotification;
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MappingToolNotification;
}
