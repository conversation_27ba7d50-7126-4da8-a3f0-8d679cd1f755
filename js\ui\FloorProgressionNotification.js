// ===== FLOOR PROGRESSION NOTIFICATION =====
// Displays an overlay when the player advances to the next floor

class FloorProgressionNotification {
    constructor() {
        this.modal = null;
        this.isShowing = false;
        this.imageLoaded = false;
        this.init();
    }
    
    init() {
        // Get the modal element
        this.modal = document.getElementById('floorProgressionModal');
        if (!this.modal) {
            console.error('Floor progression modal not found in DOM');
            return;
        }
        
        // Set up click handler to dismiss the notification
        this.modal.addEventListener('click', () => {
            this.dismissNotification();
        });
        
        // Preload the dungeon image
        this.preloadImage();
        
        console.log('Floor progression notification system initialized');
    }
    
    // Preload the dungeon image
    async preloadImage() {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => {
                this.imageLoaded = true;
                console.log('Dungeon image preloaded successfully');
                resolve();
            };
            img.onerror = () => {
                console.error('Failed to preload dungeon image');
                reject();
            };
            img.src = 'assets/images/dungeon.png';
        });
    }
    
    // Show the floor progression notification
    async showNotification(floorNumber) {
        if (!this.modal || this.isShowing) return;
        
        console.log(`Showing floor progression notification for floor ${floorNumber}`);
        
        // Ensure the image is loaded before showing
        if (!this.imageLoaded) {
            await this.preloadImage();
        }
        
        // Update the floor message
        const titleElement = this.modal.querySelector('.floor-progression-title');
        const descriptionElement = this.modal.querySelector('.floor-progression-description');
        
        if (titleElement) {
            titleElement.textContent = `ENTERING FLOOR ${floorNumber}`;
        }
        
        if (descriptionElement) {
            if (floorNumber === 2) {
                descriptionElement.innerHTML = `You descend deeper into the dungeon...<br>The darkness grows thicker around you.`;
            } else if (floorNumber <= 3) {
                descriptionElement.innerHTML = `Descending to Floor ${floorNumber}...<br>The air grows colder as you venture deeper.`;
            } else if (floorNumber <= 7) {
                descriptionElement.innerHTML = `Entering the Deep levels...<br>Ancient evil stirs in the shadows below.`;
            } else {
                descriptionElement.innerHTML = `Descending into the Abyssal depths...<br>Few have ventured this far and returned.`;
            }
        }
        
        // Show the modal
        this.modal.classList.add('active');
        this.isShowing = true;
        
        // Add a slight delay for better visual effect
        setTimeout(() => {
            if (this.modal) {
                this.modal.style.opacity = '1';
            }
        }, 50);
        
        // Auto-dismiss after 5 seconds if user doesn't click
        setTimeout(() => {
            if (this.isShowing) {
                this.dismissNotification();
            }
        }, 5000);
    }
    
    // Dismiss the notification
    dismissNotification() {
        if (!this.modal || !this.isShowing) return;
        
        console.log('Dismissing floor progression notification');
        
        // Fade out effect
        this.modal.style.opacity = '0';
        
        // Hide the modal after fade out
        setTimeout(() => {
            if (this.modal) {
                this.modal.classList.remove('active');
                this.isShowing = false;
            }
        }, 300);
    }
    
    // Check if notification is currently showing
    isNotificationShowing() {
        return this.isShowing;
    }

    // Debug method to test the notification
    testNotification(floorNumber = 2) {
        console.log(`Testing floor progression notification for floor ${floorNumber}...`);
        this.showNotification(floorNumber);
    }
}

// Create global instance
const floorProgressionNotification = new FloorProgressionNotification();

// Make it available globally for testing
window.floorProgressionNotification = floorProgressionNotification;
